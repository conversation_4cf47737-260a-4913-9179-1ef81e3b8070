{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:best-practices", "customManagers:dockerfileVersions", ":disableRateLimiting", ":semanticCommitsDisabled", "github>pehbehbeh/renovate-config//workarounds/mixGitVersioning", "github>pehbehbeh/renovate-config//customManagers/hexEsbuild", "github>pehbehbeh/renovate-config//customManagers/hexTailwind", ":reviewer(krns)", ":automergePatch", "helpers:pinGitHubActionDigestsToSemver"], "lockFileMaintenance": {"enabled": true}, "additionalBranchPrefix": "{{packageFileDir}}-", "packageRules": [{"description": "Group dependency updates to the Elixir base image", "groupName": "Base Image", "matchDepNames": ["erlang", "elixir", "ubuntu"], "pinDigests": false}, {"description": "Disable ubuntu major updates and digest pinning", "matchDatasources": ["docker"], "matchDepNames": ["ubuntu"], "matchUpdateTypes": ["major", "pinDigest"], "enabled": false}, {"description": "Disable automerge for docker data source", "matchDatasources": ["docker"], "automerge": false}, {"matchManagers": ["mix"], "rangeStrategy": "update-lockfile"}, {"description": "Automatically merge github actions updates", "matchManagers": ["github-actions"], "matchUpdateTypes": ["minor", "patch", "pin", "pinDigest"], "automerge": true}, {"description": "Label dependency updates in demo directory", "matchFileNames": ["demo/**", "Dockerfile", "compose.yml"], "labels": ["ignore-for-release", "demo", "dependencies", "{{categories}}"]}, {"description": "Label dependency updates in ci.yml", "matchFileNames": [".github/workflows/ci.yml"], "labels": ["ignore-for-release", "dependencies", "{{categories}}"]}], "labels": ["dependencies", "{{categories}}"]}