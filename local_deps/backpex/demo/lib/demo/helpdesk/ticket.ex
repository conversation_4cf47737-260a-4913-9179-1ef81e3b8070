defmodule Demo.Helpdesk.Ticket do
  @moduledoc false

  use Ash.Resource,
    domain: Demo.Helpdesk,
    data_layer: AshPostgres.DataLayer

  postgres do
    repo Demo.Repo
    table "tickets"
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [:subject, :body]
    end

    update :update do
      accept [:subject, :body]
    end
  end

  attributes do
    uuid_primary_key :id
    attribute :subject, :string, allow_nil?: false
    attribute :body, :string, allow_nil?: false
    create_timestamp :inserted_at
    update_timestamp :updated_at
  end
end
