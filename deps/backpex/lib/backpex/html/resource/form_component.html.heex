<div id="form-component">
  <.form
    :let={f}
    for={@form}
    id="resource-form"
    phx-target={@myself}
    phx-change="validate"
    phx-submit="save"
    phx-value-action-key={
      if @action_type == :item do
        @action_to_confirm.key
      else
        nil
      end
    }
    multipart
  >
    <.edit_card>
      <%!-- Default panel --%>
      <:panel>
        <.resource_form_field
          :for={{name, _field_options} <- visible_fields_by_panel(@fields, :default, assigns)}
          name={name}
          form={f}
          {assigns}
        />
      </:panel>

      <%!-- Custom panels --%>
      <:panel
        :for={{name, label} <- @panels}
        :if={visible_fields_by_panel(@fields, name, assigns) != []}
        label={label}
        class="mt-4"
      >
        <.resource_form_field
          :for={{name, _field_options} <- visible_fields_by_panel(@fields, name, assigns)}
          name={name}
          form={f}
          {assigns}
        />
      </:panel>

      <:actions>
        <div :if={form_errors?(@show_form_errors, f)}>
          <p class="text-error text-sm italic">
            {Backpex.__("There are errors in the form.", @live_resource)}
          </p>
        </div>
        <%= if @action_type == :item do %>
          <button type="button" class="btn" phx-click={JS.exec("data-cancel", to: {:closest, "dialog"})}>
            {@action_to_confirm.module.cancel_label(assigns)}
          </button>

          <button
            type="submit"
            class={["btn", if(form_errors?(@show_form_errors, f), do: "btn-error", else: "btn-primary")]}
            phx-disable-with={@action_to_confirm.module.confirm_label(assigns) <> "..."}
          >
            {@action_to_confirm.module.confirm_label(assigns)}
          </button>
        <% else %>
          <.link navigate={@live_resource.return_to(@socket, assigns, @live_action, :cancel, @item)}>
            <button type="button" class="btn">
              {Backpex.__("Cancel", @live_resource)}
            </button>
          </.link>
          <button
            :if={@continue_label}
            type="submit"
            name="save-type"
            value="continue"
            class={["btn btn-soft", if(form_errors?(@show_form_errors, f), do: "btn-error", else: "btn-primary")]}
            phx-disable-with={@continue_label <> "..."}
          >
            {@continue_label}
          </button>
          <button
            type="submit"
            name="save-type"
            value="save"
            class={["btn", if(form_errors?(@show_form_errors, f), do: "btn-error", else: "btn-primary")]}
            phx-disable-with={@save_label <> "..."}
          >
            {@save_label}
          </button>
        <% end %>
      </:actions>
    </.edit_card>
  </.form>
</div>
