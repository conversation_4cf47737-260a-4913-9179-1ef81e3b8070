# Upgrading to v0.2

## Bump Your Deps

Update Backpex to the latest version:

```elixir
  defp deps do
    [
      {:backpex, "~> 0.2.0"}
    ]
  end
```

## Pass `assigns` to `init_change` functions

We change the arity of the `init_change/0` function to `init_change/1` for resource and item actions.

The param will be the assigns. This adds more ways to construct an initial change.

If you had such a function in your resource or item action:

```elixir
@impl Backpex.ItemAction
def init_change() do
  # construct init change
end
```

You need to change it to this:

```elixir
@impl Backpex.ItemAction
def init_change(_assigns) do
  # construct init change
end
```

## Change arity of changeset functions

See [Pull Request](https://github.com/naymspace/backpex/pull/94).

We are introducing a new way to pass metadata to changeset functions. Previously, we supported changeset functions with
different arities to optionally pass the `assigns` and `target` name to changesets.

With this release, we require the arity of all changeset functions you provide to be `3`.

The metadata, previously passed as additional parameters, is now passed as a keyword list to changesets as the third parameter.

Currently we pass the following metadata to changesets:

- `:assigns` - the assigns
- `:target` - the name of the `form` target that triggered the changeset call


When you previously had a LiveResource and Schema that looked like the following:

```elixir
defmodule MyAppWeb.UserLive do
  use Backpex.LiveResource,
      ...
      update_changeset: &MyApp.User.changeset/2,
      create_changeset: &MyApp.User.changeset/2,
end


defmodule MyApp.User do
  ...

  def changeset(user, attrs) do
      ...
  end
end
```

You have to change it to this:

```elixir
defmodule MyAppWeb.UserLive do
  use Backpex.LiveResource,
    ...
    update_changeset: &MyApp.User.changeset/3,
    create_changeset: &MyApp.User.changeset/3,
end


defmodule MyApp.User do
  ...

  def changeset(user, attrs, metadata) do
    # fetch assigns from metadata
    assigns = Keyword.get(metadata, :assigns)

    # fetch target from metadata
    assigns = Keyword.get(metadata, :target)

    ...
  end
end
```

This change applies to Resource and Item Actions as well.

For example:

```elixir
defmodule MyApp.EmailResourceAction
  use Backpex.ResourceAction

  @impl Backpex.ResourceAction
  def changeset(change, attrs, _metadata \\ []) do
    ...
  end
end
```

## Update prompt option

With v0.2 we are updating the prompt option for fields. This affects to the following fields: `BelongsTo`, `Select`, `HasMany`, `ManyToMany` and `MultiSelect`. 
The `prompt` option can be raw text or a function that takes the assigns and returns text. 
We no longer support adding additional options.

If you previously had a field that looked like the following:

```elixir
def fields do
  [
    user: %{
      module: Backpex.Fields.BelongsTo,
      label: "Author",
      prompt: [key: "Please select an author", disabled: true],
      ...
    },
  ]
end
```

You need to change it to the following:

```elixir
def fields do
  [
    user: %{
      module: Backpex.Fields.BelongsTo,
      label: "Author",
      prompt: "Please select an author",
      ...
    },
  ]
end
```