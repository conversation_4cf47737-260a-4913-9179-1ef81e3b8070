# Upgrading to v0.6

## Bump Your Deps

Update Backpex to the latest version:

```elixir
  defp deps do
    [
      {:backpex, "~> 0.6.0"}
    ]
  end
```

## Change `Backpex.Fields.ManyToMany` to `Backpex.Fields.HasMany`

With version 0.6, we have combined the `Backpex.Fields.ManyToMany` and `Backpex.Fields.HasMany` field. The functionality of the fields is now combined in the `Backpex.Fields.HasMany` field. The API of the field has not changed, so you can simply replace `Backpex.Fields.ManyToMany` with `Backpex.Fields.HasMany` in your resource configuration.