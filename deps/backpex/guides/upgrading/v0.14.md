# Upgrading to v0.14

## Bump Your Deps

Update Backpex to the latest version:

```elixir
  defp deps do
    [
      {:backpex, "~> 0.14.0"}
    ]
  end
```

## Filter callback functions have been changed

We now pass the assigns to the `query` and `options` filter callbacks. Therefore, the arity of these functions has changed.

- [`Backpex.Filter.query/3`]() -> `c:Backpex.Filter.query/4`
- [`Backpex.Filters.Boolean.options/0`]() -> `c:Backpex.Filters.Boolean.options/1`
- [`Backpex.Filters.MultiSelect.options/0`]() -> `c:Backpex.Filters.MultiSelect.options/1`
- [`Backpex.Filters.Select.options/0`]() -> `c:Backpex.Filters.Select.options/1`

If you have implemented any of the above callbacks, make sure to change your filters.

Before:

```elixir
defmodule MyAppWeb.Filters.PostPublished do
  use Backpex.Filters.Boolean

  @impl Backpex.Filter
  def label, do: "Published?"

  @impl Backpex.Filters.Boolean
  def options do
    [
      ...
    ]
  end
end
```

After:

```elixir
defmodule MyAppWeb.Filters.PostPublished do
  use Backpex.Filters.Boolean

  @impl Backpex.Filter
  def label, do: "Published?"

  @impl Backpex.Filters.Boolean
  def options(_assigns) do
    [
      ...
    ]
  end
end
```

## `input/1` component has been updated

We've updated the `Backpex.HTML.Form.input/1` component:

- `input_wrapper_class` attribute has been removed as it was used by the select input only
- new `error` class attribute to provide an error class to user over the defaults
- `legend` element was replaced by `div` element to align with Phoenix CoreComponents

If you use the `input/1` make sure to update your code to accommodate these breaking changes, particularly removing any references to the deprecated `input_wrapper_class` attribute.
