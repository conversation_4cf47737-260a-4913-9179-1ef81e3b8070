# What is a LiveResource?

When refer to a *LiveResource* in Backpex, we are talking about a module that contains the configuration for a resource. This module is responsible for defining the resource's schema, the actions that can be performed on it, and the fields that will be rendered.

In the documentation, we also talk about the resource configuration file. With this, we refer to the module that implements the `Backpex.LiveResource` behavior. This is a Backpex *LiveResource* module.
