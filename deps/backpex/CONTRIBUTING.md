<!-- omit in toc -->
# Contributing to Backpex

First off, thanks for taking the time to contribute! ❤️

All types of contributions are encouraged and valued. See the [Table of Contents](#table-of-contents) for different ways to help and details about how this project handles them. Please make sure to read the relevant section before making your contribution. It will make it a lot easier for us maintainers and smooth out the experience for all involved. The community looks forward to your contributions. 🎉

> And if you like the project, but just don't have time to contribute, that's fine. There are other easy ways to support the project and show your appreciation, which we would also be very happy about:
> - Star the project
> - Tweet about it
> - Refer this project in your project's readme
> - Mention the project at local meetups and tell your friends/colleagues

<!-- omit in toc -->
## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [I Have a Question](#i-have-a-question)
- [I Want To Contribute](#i-want-to-contribute)
- [Reporting Bugs](#reporting-bugs)
- [Suggesting Enhancements](#suggesting-enhancements)
- [Your First Code Contribution](#your-first-code-contribution)
- [Styleguides](#styleguide)
- [Commit Messages](#commit-messages)

## Code of Conduct

This project and everyone participating in it is governed by the
[Backpex Code of Conduct](https://github.com/naymspace/backpex/blob/main/CODE_OF_CONDUCT.md).
By participating, you are expected to uphold this code.


## I Have a Question

> If you want to ask a question, we assume that you have read the available [Documentation](https://hexdocs.pm/backpex).

Before you ask a question, it is best to search for existing [Issues](https://github.com/naymspace/backpex/issues) and [Discussions](https://github.com/naymspace/backpex/discussions) that might help you. In case you have found a suitable issue or discussion and still need clarification, you can write your question in this issue or discussion. It is also advisable to search the internet for answers first.

If you then still feel the need to ask a question and need clarification, we recommend the following:

- Open a [discussion](https://github.com/naymspace/backpex/discussions/new?category=q-a) in the Q&A category.
- Provide as much context as you can about what you're running into.
- Provide project and platform versions (nodejs, npm, etc), depending on what seems relevant.

We will then take care of the discussion and provide an answer as soon as possible.

## I Want To Contribute

> ### Legal Notice <!-- omit in toc -->
> When contributing to this project, you must agree that you have authored 100% of the content, that you have the necessary rights to the content and that the content you contribute may be provided under the project license.

### Reporting Bugs

<!-- omit in toc -->
#### Before Submitting a Bug Report

A good bug report shouldn't leave others needing to chase you up for more information. Therefore, we ask you to investigate carefully, collect information and describe the issue in detail in your report. Please complete the following steps in advance to help us fix any potential bug as fast as possible.

- Make sure that you are using the latest version.
- Determine if your bug is really a bug and not an error on your side e.g. using incompatible environment components/versions (Make sure that you have read the [documentation](https://hexdocs.pm/backpex). If you are looking for support, you might want to check [this section](#i-have-a-question)).
- To see if other users have experienced (and potentially already solved) the same issue you are having, check if there is not already a bug report existing for your bug or error in the [bug tracker](https://github.com/naymspace/backpex/issues?q=label%3Abug).
- Also make sure to search the internet to see if users outside of the GitHub community have discussed the issue.
- Collect information about the bug:
- Stack trace (Traceback)
- OS, Platform and Version (Windows, Linux, macOS, x86, ARM)
- Version of the interpreter, compiler, SDK, runtime environment, package manager, depending on what seems relevant.
- Possibly your input and the output
- Can you reliably reproduce the issue? And can you also reproduce it with older versions?

<!-- omit in toc -->
#### How Do I Submit a Good Bug Report?

> You must never report security related issues, vulnerabilities or bugs including sensitive information to the issue tracker, or elsewhere in public. Instead please choose "Report a security vulnerability" when [choosing the issue category](https://github.com/naymspace/backpex/issues/new/choose).

We use GitHub issues to track bugs and errors. If you run into an issue with the project:

- Open an [Issue](https://github.com/naymspace/backpex/issues/new/choose) and select "Bug report". (Since we can't be sure at this point whether it is a bug or not, we ask you not to talk about a bug yet and not to label the issue.)
- Explain the behavior you would expect and the actual behavior.
- Please provide as much context as possible and describe the *reproduction steps* that someone else can follow to recreate the issue on their own. This usually includes your code. For good bug reports you should isolate the problem and create a reduced test case.
- Provide the information you collected in the previous section.
- Use the provided template for your bug report.

Once it's filed:

- The project team will label the issue accordingly.
- A team member will try to reproduce the issue with your provided steps. If there are no reproduction steps or no obvious way to reproduce the issue, the team will ask you for those steps.
- If the team is able to reproduce the issue, the team may add additional tags, and the issue will be left to be [implemented by someone](#your-first-code-contribution).

### Suggesting Enhancements

This section guides you through submitting an enhancement suggestion for Backpex, **including completely new features and minor improvements to existing functionality**. Following these guidelines will help maintainers and the community to understand your suggestion and find related suggestions.

<!-- omit in toc -->
#### Before Submitting an Enhancement

- Make sure that you are using the latest version.
- Read the [documentation](https://hexdocs.pm/backpex) carefully and find out if the functionality is already covered, maybe by an individual configuration.
- Perform a search to see if the enhancement has already been suggested. You may search [discussions](https://github.com/naymspace/backpex/discussions/categories/ideas) in the "Ideas" category, [issues](https://github.com/naymspace/backpex/issues), the [development board](https://github.com/orgs/naymspace/projects/1) or the [roadmap](https://github.com/orgs/naymspace/projects/2).
- Find out whether your idea fits with the scope and aims of the project. It's up to you to make a strong case to convince the project's developers of the merits of this feature. Keep in mind that we want features that will be useful to the majority of our users and not just a small subset. If you're just targeting a minority of users, consider writing an add-on/plugin library.

<!-- omit in toc -->
#### How Do I Submit a Good Enhancement Suggestion?

Enhancement suggestions are tracked as [GitHub discussions](https://github.com/naymspace/backpex/discussions). You may submit enhancement suggestions and ideas to [the "Ideas" category](https://github.com/naymspace/backpex/discussions/categories/ideas).

- Use a **clear and descriptive title** for the discussion to identify the suggestion.
- Provide a **step-by-step description of the suggested enhancement** in as many details as possible.
- **Describe the current behavior** and **explain which behavior you expected to see instead** and why. At this point you can also tell which alternatives do not work for you.
- You may want to **include screenshots and animated GIFs** which help you demonstrate the steps or point out the part which the suggestion is related to.
- **Explain why this enhancement would be useful** to most Backpex users. You may also want to point out the other projects that solved it better and which could serve as inspiration.

### What can I contribute?

We provide a roadmap and a list of issues that you can work on to contribute to Backpex.

- Roadmap: https://github.com/orgs/naymspace/projects/2
- Issues: https://github.com/naymspace/backpex/issues
- Especially, issues labeled with `good-first-issue` are a good starting point for new contributors.

We also use GitHub's discussion feature to discuss new ideas and features.

- Discussions: https://github.com/naymspace/backpex/discussions

If you don't find an issue that you want to work on, you can always contribute to Backpex by:

- Reporting bugs (create an issue)
- Requesting new features (use the discussions)
- Improving the documentation
- Improving the demo application

### Your First Code Contribution

#### Fork the repository

In order to contribute to Backpex, you need to fork the repository. You can do this by clicking the "Fork" button in the top right corner of the repository page at [https://github.com/naymspace/backpex](https://github.com/naymspace/backpex).

#### Clone the repository

After forking the repository, you need to clone it to your local machine. You can do this by running the `git clone` command along with the URL of your forked repository.

#### Setting up your development environment

You first need to create a `.env` file in the `demo` directory of the project with the following content:

```bash
SECRET_KEY_BASE=<SECRET_KEY_BASE>
LIVE_VIEW_SIGNING_SALT=<LIVE_VIEW_SIGNING_SALT>
```

For development purposes you can copy the values from the `demo/.env.example` file.

You can then start the development environment by running the following command in the root directory of the project:

```bash
docker compose up
```

Backpex comes with a demo application that you can use to test the features of the project. The command will start a PostgreSQL database and the demo application on [http://localhost:4000](http://localhost:4000).

To insert some demo data into the database, you can run the following command:

```bash
docker compose exec app mix ecto.seed
```

**Making changes**

After setting up your development environment, you can start making changes to the project. We recommend creating a new branch for your changes. After submitting your changes to your forked repository, you can create a pull request to the `develop` branch of the main repository.

### Styleguide

We value concise, readable, and high-quality code. While we don't have a formal styleguide, please adhere to these principles:

#### General Guidelines

- Follow the official Elixir Guidelines.
- Use `mix format` before committing your code.
- Keep functions small and focused on a single responsibility.
- Use meaningful variable and function names.
- Prefer pattern matching in function heads over conditional logic in the function body.
- Use pipeline operator `|>` when it improves readability.

#### Documentation

- Document public functions using `@doc` and `@moduledoc`.
- Include examples in your documentation when appropriate.

#### Testing

- Write tests for all public functions.
- Use descriptive test names that explain the expected behavior.

### Commit Messages

We value clear and informative commit messages. While we don't have strict rules, please follow these guidelines:

- Use the imperative mood in the subject line (e.g., "Add feature" not "Added feature").
- Limit the subject line to 50 characters.
- Separate subject from body with a blank line.
- Use the body to explain what and why, not how.

> Note: We may squash commits before merging. Ensure each commit represents a logical unit of change, but don't worry too much about having a perfect commit history.

## Attribution

This guide is based on the **contributing-gen**. [Make your own](https://github.com/bttger/contributing-gen)!