# Hall Activity 活动测试指南

## 概述
本文档提供了 hall_activity 各个活动的测试方法和改进建议，重点关注如何快速测试时间相关的功能。

## 现有测试支持

### TestDataManager 测试工具
系统已提供了 `Teen.ActivitySystem.TestDataManager` 模块，仅在开发和测试环境可用：

```elixir
# 时间模拟 - 模拟时间推进
Teen.ActivitySystem.TestDataManager.simulate_time_advance(user_id, days_forward, operator)

# 清除用户活动数据
Teen.ActivitySystem.TestDataManager.clear_user_activity_data(user_id, activity_types, operator)

# 重置活动进度
Teen.ActivitySystem.TestDataManager.reset_activity_progress(user_id, activity_type, operator)
```

## 各活动测试方法

### 1. 7日签到活动 (Seven Days Sign-in)

**当前实现**：
- 通过 `RewardClaimRecord` 检查是否已签到
- 检查 `reward_data` 中的 `day` 字段判断已签到天数

**测试方法**：
```elixir
# 使用 TestDataManager 模拟时间推进
{:ok, result} = Teen.ActivitySystem.TestDataManager.simulate_time_advance(
  user_id, 
  1,  # 推进1天
  %{id: admin_id, ip_address: "127.0.0.1"}
)

# 清除特定天的签到记录（如需重复测试）
Teen.ActivitySystem.TestDataManager.clear_user_activity_data(
  user_id, 
  [:seven_day_task], 
  operator
)
```

**改进建议**：
1. 添加测试模式配置，允许修改签到冷却时间
2. 提供直接修改签到天数的测试接口
3. 允许在测试环境中覆盖 UTC 时间检查

### 2. 首充礼包活动 (First Recharge Gift)

**当前实现**：
- 基于用户注册时间的 `limit_days` 限制
- 使用 `get_by_user_days` 查询符合条件的礼包

**测试方法**：
```elixir
# 修改用户注册时间来测试过期情况
user = Cypridina.Accounts.User |> Ash.get!(user_id)
{:ok, updated_user} = user 
  |> Ash.Changeset.for_update(:update, %{
    inserted_at: DateTime.utc_now() |> DateTime.add(-8, :day)  # 模拟8天前注册
  })
  |> Ash.update()

# 或者修改礼包的 limit_days 配置
{:ok, gift} = Teen.ActivitySystem.FirstRechargeGift.get_by_user_days(7)
{:ok, updated} = gift
  |> Ash.Changeset.for_update(:update, %{limit_days: 30})
  |> Ash.update()
```

**改进建议**：
1. 添加测试模式下的"用户天数覆盖"功能
2. 提供快速切换礼包状态的测试接口
3. 增加测试专用的时间计算函数

### 3. VIP礼包活动 (VIP Gift)

**当前实现**：
- 使用 `last_daily_claim`, `last_weekly_claim`, `last_monthly_claim` 记录领取时间
- `can_claim_again?/2` 函数检查时间间隔

**测试方法**：
```elixir
# 直接修改用户参与记录中的时间戳
participation = Teen.ActivitySystem.UserActivityParticipation
  |> Ash.Query.filter(user_id == ^user_id and activity_type == :vip_gift)
  |> Ash.read!()
  |> List.first()

# 修改上次领取时间为25小时前（可以再次领取每日奖励）
new_data = Map.put(
  participation.participation_data || %{}, 
  "last_daily_claim", 
  DateTime.utc_now() |> DateTime.add(-25, :hour) |> DateTime.to_iso8601()
)

{:ok, _} = participation
  |> Ash.Changeset.for_update(:update, %{participation_data: new_data})
  |> Ash.update()
```

**改进建议**：
1. 添加测试模式下的时间间隔配置（如将24小时改为1分钟）
2. 提供批量重置VIP礼包领取状态的功能
3. 增加"立即可领取"的测试覆盖函数

## 通用改进建议

### 1. 添加测试配置模块
创建 `Teen.ActivitySystem.TestConfig` 模块：

```elixir
defmodule Teen.ActivitySystem.TestConfig do
  @moduledoc """
  测试环境专用配置
  """
  
  def get_config(key, default \\ nil) do
    if Application.get_env(:cypridina, :environment) in [:dev, :test] do
      Application.get_env(:cypridina, :activity_test_config, %{})
      |> Map.get(key, default)
    else
      default
    end
  end
  
  # 示例配置项
  def sign_in_cooldown_hours, do: get_config(:sign_in_cooldown_hours, 24)
  def vip_daily_cooldown_hours, do: get_config(:vip_daily_cooldown_hours, 24)
  def vip_weekly_cooldown_days, do: get_config(:vip_weekly_cooldown_days, 7)
  def vip_monthly_cooldown_days, do: get_config(:vip_monthly_cooldown_days, 30)
  def first_recharge_limit_days_override, do: get_config(:first_recharge_limit_days_override, nil)
end
```

### 2. 增强时间模拟功能
扩展 `TestDataManager.simulate_time_advance/3`：

```elixir
def simulate_time_advance(user_id, time_unit, amount, operator) do
  # 支持更精细的时间单位
  # time_unit: :hour | :day | :week | :month
  # amount: 推进的数量
end
```

### 3. 添加活动状态快照和恢复
```elixir
def snapshot_activity_state(user_id, activity_types \\ [])
def restore_activity_state(user_id, snapshot_id)
```

### 4. 提供测试数据生成器
```elixir
def generate_test_scenario(scenario_type, user_id) do
  case scenario_type do
    :seven_days_partial -> 
      # 生成已签到3天的测试数据
    :vip_all_claimable ->
      # 生成所有VIP奖励都可领取的状态
    :first_recharge_expiring ->
      # 生成即将过期的首充礼包状态
  end
end
```

### 5. 添加测试专用的时间覆盖
在活动检查函数中添加测试时间覆盖：

```elixir
defp get_current_time do
  case Teen.ActivitySystem.TestConfig.get_config(:override_current_time) do
    nil -> DateTime.utc_now()
    time_string -> DateTime.from_iso8601!(time_string)
  end
end
```

## 测试流程示例

### 完整的7日签到测试流程：
```elixir
# 1. 清除现有数据
{:ok, _} = TestDataManager.clear_user_activity_data(user_id, [:seven_day_task], operator)

# 2. 第1天签到
# 调用签到接口...

# 3. 模拟时间推进到第2天
{:ok, _} = TestDataManager.simulate_time_advance(user_id, 1, operator)

# 4. 第2天签到
# 调用签到接口...

# 5. 快速推进到第7天
{:ok, _} = TestDataManager.simulate_time_advance(user_id, 5, operator)

# 6. 完成第7天签到
# 调用签到接口...
```

## 注意事项

1. **环境限制**：所有测试功能仅在 `:dev` 和 `:test` 环境可用
2. **数据安全**：测试数据修改会记录在 `OperationLog` 中
3. **事务处理**：批量操作在事务中执行，确保数据一致性
4. **权限控制**：需要提供操作员信息用于审计

## 总结

当前的活动系统已具备基本的测试支持，但在时间相关功能的测试便利性上仍有改进空间。通过实施上述建议，可以大幅提升测试效率，特别是：

1. 无需等待真实时间流逝即可测试所有时间相关功能
2. 可以快速构建各种测试场景
3. 测试数据的创建和清理更加便捷
4. 保持测试代码与生产代码的清晰分离

建议优先实施测试配置模块和增强的时间模拟功能，这将为所有活动类型的测试提供统一的基础设施。