defmodule Teen.ItemActions.RefreshPaymentStatus do
  @moduledoc """
  刷新支付状态的项目操作
  """

  use Backpex.ItemAction

  import Phoenix.Component

  require Logger

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "刷新状态"

  @impl Backpex.ItemAction
  def icon(assigns, _item) do
    ~H"""
    <Backpex.HTML.CoreComponents.icon
      name="hero-arrow-path"
      class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-blue-600"
    />
    """
  end


  def confirm_label(_assigns), do: "确认刷新"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "确定要刷新选中订单的状态吗？"

  @impl Backpex.ItemAction
  def fields, do: []

  def can?(_assigns, item) do
    # 只有待处理或处理中的订单才能刷新状态
    case item do
      %{status: status} -> status in [0, 1, "pending", "processing"]
      %{payment_status: status} -> status in [0, 1, "pending", "processing"]
      _ -> true
    end
  end

  @impl Backpex.ItemAction
  def handle(socket, items, _params) do
    Logger.info("🔄 [REFRESH_PAYMENT_STATUS] handle called with #{length(items)} items")
    
    # 批量刷新支付状态
    results = 
      items
      |> Enum.map(fn item ->
        Logger.info("🔄 [REFRESH_PAYMENT_STATUS] Processing item #{item.id}")
        
        case refresh_payment_status(item) do
          {:ok, _} -> 
            Logger.info("🔄 [REFRESH_PAYMENT_STATUS] Successfully refreshed payment status for order #{item.id}")
            {:ok, item.id}
          {:error, reason} -> 
            Logger.error("🔄 [REFRESH_PAYMENT_STATUS] Failed to refresh payment status for order #{item.id}: #{inspect(reason)}")
            {:error, {item.id, reason}}
        end
      end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    failed_count = Enum.count(results, fn {status, _} -> status == :error end)

    Logger.info("🔄 [REFRESH_PAYMENT_STATUS] Results: #{success_count} success, #{failed_count} failed")

    socket = 
      if success_count > 0 do
        message = "成功刷新 #{success_count} 个订单状态"
        message = if failed_count > 0 do
          message <> "，#{failed_count} 个失败"
        else
          message
        end

        socket
        |> Phoenix.LiveView.put_flash(:info, message)
      else
        socket
        |> Phoenix.LiveView.put_flash(:error, "刷新订单状态失败，请检查选中的订单")
      end

    {:ok, socket}
  end

  defp refresh_payment_status(item) do
    try do
      case item.__struct__ do
        Teen.PaymentSystem.PaymentOrder ->
          # 刷新订单状态 - 使用通用更新方法
          # TODO: 实现与支付网关的状态同步
          {:ok, item}

        _ ->
          # 通用的状态刷新方法
          # 这里可以调用第三方支付接口查询最新状态
          {:ok, item}
      end
    rescue
      error -> {:error, error}
    end
  end
end
