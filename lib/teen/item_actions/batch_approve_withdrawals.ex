defmodule Teen.ItemActions.BatchApproveWithdrawals do
  @moduledoc """
  批量审核通过提现申请的项目操作
  """

  use Backpex.ItemAction

  import Phoenix.Component

  require Logger

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "批量审核通过"

  @impl Backpex.ItemAction
  def icon(assigns, _item) do
    ~H"""
    <Backpex.HTML.CoreComponents.icon
      name="hero-check-circle"
      class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-blue-600"
    />
    """
  end

  @impl Backpex.ItemAction
  def confirm_label(_assigns), do: "确认批量审核通过"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "确定要批量审核通过选中的提现申请吗？"

  @impl Backpex.ItemAction
  def fields, do: []

  def can?(_assigns, _item) do
    # 只有待审核状态的提现记录才能审核通过
    true
  end

  @impl Backpex.ItemAction
  def handle(socket, items, params) do
    Logger.info("💸 [BATCH_APPROVE] handle called with #{length(items)} items")
    Logger.info("💸 [BATCH_APPROVE] params: #{inspect(params)}")
    Logger.info("💸 [BATCH_APPROVE] socket type: #{inspect(socket.__struct__)}")

    # 安全地获取 current_user
    current_user = case socket do
      %Phoenix.LiveView.Socket{assigns: %{current_user: user}} -> user
      %{current_user: user} -> user
      _ ->
        Logger.error("💸 [BATCH_APPROVE] Cannot find current_user in socket")
        raise "Current user not found"
    end

    # 过滤出只有待审核状态的提现记录
    pending_withdrawals =
      items
      |> Enum.filter(fn withdrawal -> withdrawal.audit_status == 0 end)

    Logger.info("💸 [BATCH_APPROVE] Found #{length(pending_withdrawals)} pending withdrawals")

    if length(pending_withdrawals) == 0 do
      socket =
        socket
        |> Phoenix.LiveView.put_flash(:error, "没有待审核的提现申请")

      {:ok, socket}
    else
      # 批量审核通过
      results =
        pending_withdrawals
        |> Enum.map(fn withdrawal ->
          Logger.info("💸 [BATCH_APPROVE] Processing withdrawal #{withdrawal.id}")

          case Teen.Services.WithdrawalService.approve_withdrawal(
            withdrawal.id,
            current_user.id
          ) do
            {:ok, _} ->
              Logger.info("💸 [BATCH_APPROVE] Successfully approved withdrawal #{withdrawal.id}")
              {:ok, withdrawal.id}
            {:error, reason} ->
              Logger.error("💸 [BATCH_APPROVE] Failed to approve withdrawal #{withdrawal.id}: #{inspect(reason)}")
              {:error, {withdrawal.id, reason}}
          end
        end)

      success_count = Enum.count(results, fn {status, _} -> status == :ok end)
      failed_count = Enum.count(results, fn {status, _} -> status == :error end)

      Logger.info("💸 [BATCH_APPROVE] Results: #{success_count} success, #{failed_count} failed")

      socket =
        if success_count > 0 do
          message = "成功审核通过 #{success_count} 个提现申请"
          message = if failed_count > 0 do
            message <> "，#{failed_count} 个失败"
          else
            message
          end

          socket
          |> Phoenix.LiveView.put_flash(:info, message)
        else
          socket
          |> Phoenix.LiveView.put_flash(:error, "批量审核通过失败，请检查选中的提现记录")
        end

      {:ok, socket}
    end
  end
end
