defmodule Teen.ItemActions.VerifyBankCards do
  @moduledoc """
  验证银行卡的项目操作
  """

  use Backpex.ItemAction

  import Phoenix.Component

  require Logger

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "验证银行卡"

  @impl Backpex.ItemAction
  def icon(assigns, _item) do
    ~H"""
    <Backpex.HTML.CoreComponents.icon
      name="hero-shield-check"
      class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-blue-600"
    />
    """
  end


  def confirm_label(_assigns), do: "确认验证"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "确定要验证选中的银行卡吗？"

  @impl Backpex.ItemAction
  def fields, do: []

  def can?(_assigns, item) do
    # 只有未验证状态的银行卡才能验证
    case item do
      %{status: status} -> status == 0 or status == "pending"
      %{is_verified: is_verified} -> not is_verified
      %{verified: verified} -> not verified
      _ -> true
    end
  end

  @impl Backpex.ItemAction
  def handle(socket, items, _params) do
    Logger.info("💳 [VERIFY_BANK_CARDS] handle called with #{length(items)} items")
    
    # 批量验证银行卡
    results = 
      items
      |> Enum.map(fn item ->
        Logger.info("💳 [VERIFY_BANK_CARDS] Processing item #{item.id}")
        
        case verify_bank_card(item) do
          {:ok, _} -> 
            Logger.info("💳 [VERIFY_BANK_CARDS] Successfully verified bank card #{item.id}")
            {:ok, item.id}
          {:error, reason} -> 
            Logger.error("💳 [VERIFY_BANK_CARDS] Failed to verify bank card #{item.id}: #{inspect(reason)}")
            {:error, {item.id, reason}}
        end
      end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    failed_count = Enum.count(results, fn {status, _} -> status == :error end)

    Logger.info("💳 [VERIFY_BANK_CARDS] Results: #{success_count} success, #{failed_count} failed")

    socket = 
      if success_count > 0 do
        message = "成功验证 #{success_count} 张银行卡"
        message = if failed_count > 0 do
          message <> "，#{failed_count} 张失败"
        else
          message
        end

        socket
        |> Phoenix.LiveView.put_flash(:info, message)
      else
        socket
        |> Phoenix.LiveView.put_flash(:error, "验证银行卡失败，请检查选中的银行卡")
      end

    {:ok, socket}
  end

  defp verify_bank_card(item) do
    try do
      case item.__struct__ do
        Teen.PaymentSystem.UserBankCard ->
          # 验证银行卡 - 更新验证状态
          Teen.PaymentSystem.UserBankCard.update(item, %{verified_at: DateTime.utc_now()})

        _ ->
          # 通用的验证方法
          update_attrs = case item do
            %{status: _} -> %{status: 1}  # 已验证
            %{is_verified: _} -> %{is_verified: true}
            %{verified: _} -> %{verified: true}
            _ -> %{verified: true}
          end

          case item.__struct__.update(item, update_attrs) do
            {:ok, updated_item} -> {:ok, updated_item}
            {:error, changeset} -> {:error, changeset}
          end
      end
    rescue
      error -> {:error, error}
    end
  end
end
