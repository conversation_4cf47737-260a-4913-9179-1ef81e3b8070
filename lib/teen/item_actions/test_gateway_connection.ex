defmodule Teen.ItemActions.TestGatewayConnection do
  @moduledoc """
  测试支付网关连接的项目操作
  """

  use Backpex.ItemAction

  import Phoenix.Component

  require Logger

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "测试连接"

  @impl Backpex.ItemAction
  def icon(assigns, _item) do
    ~H"""
    <Backpex.HTML.CoreComponents.icon
      name="hero-wifi"
      class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-blue-600"
    />
    """
  end


  def confirm_label(_assigns), do: "确认测试"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "确定要测试选中网关的连接吗？"

  @impl Backpex.ItemAction
  def fields, do: []

  def can?(_assigns, _item) do
    # 所有网关都可以测试连接
    true
  end

  @impl Backpex.ItemAction
  def handle(socket, items, _params) do
    Logger.info("🔗 [TEST_GATEWAY_CONNECTION] handle called with #{length(items)} items")

    # 批量测试网关连接
    results =
      items
      |> Enum.map(fn item ->
        Logger.info("🔗 [TEST_GATEWAY_CONNECTION] Processing item #{item.id}")

        case test_gateway_connection(item) do
          {:ok, _} ->
            Logger.info("🔗 [TEST_GATEWAY_CONNECTION] Successfully tested gateway connection #{item.id}")
            {:ok, item.id}
          {:error, reason} ->
            Logger.error("🔗 [TEST_GATEWAY_CONNECTION] Failed to test gateway connection #{item.id}: #{inspect(reason)}")
            {:error, {item.id, reason}}
        end
      end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    failed_count = Enum.count(results, fn {status, _} -> status == :error end)

    Logger.info("🔗 [TEST_GATEWAY_CONNECTION] Results: #{success_count} success, #{failed_count} failed")

    socket =
      if success_count > 0 do
        message = "成功测试 #{success_count} 个网关连接"
        message = if failed_count > 0 do
          message <> "，#{failed_count} 个失败"
        else
          message
        end

        socket
        |> Phoenix.LiveView.put_flash(:info, message)
      else
        socket
        |> Phoenix.LiveView.put_flash(:error, "测试网关连接失败，请检查选中的网关")
      end

    {:ok, socket}
  end

  defp test_gateway_connection(item) do
    try do
      case item.__struct__ do
        Teen.PaymentSystem.PaymentGateway ->
          Teen.PaymentSystem.PaymentGateway.test_connection(item)

        _ ->
          # 通用的连接测试方法
          # 这里可以实现 HTTP 请求测试网关连接
          case test_http_connection(item) do
            {:ok, _} -> {:ok, :connected}
            {:error, reason} -> {:error, reason}
          end
      end
    rescue
      error -> {:error, error}
    end
  end

  defp test_http_connection(gateway) do
    try do
      # 构建测试 URL
      test_url = build_test_url(gateway)

      # 发送 HTTP 请求测试连接 (使用 Finch 替代 HTTPoison)
      case Finch.build(:get, test_url) |> Finch.request(CypridinaWeb.Finch, receive_timeout: 5000) do
        {:ok, %Finch.Response{status: status}} when status in 200..299 ->
          {:ok, :connected}
        {:ok, %Finch.Response{status: status}} ->
          {:error, "HTTP #{status}"}
        {:error, reason} ->
          {:error, reason}
      end
    rescue
      error -> {:error, error}
    end
  end

  defp build_test_url(gateway) do
    base_url = gateway.gateway_url || ""

    # 如果有查询接口路径，使用查询接口测试
    if gateway.query_order_path do
      Path.join(base_url, gateway.query_order_path)
    else
      base_url
    end
  end
end
