defmodule Teen.ItemActions.UnbanUser do
  @moduledoc """
  解封用户的项目操作
  """

  use Backpex.ItemAction

  import Phoenix.Component

  import Ecto.Changeset
  require Logger

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "解封用户"

  @impl Backpex.ItemAction
  def icon(assigns, _item) do
    ~H"""
    <Backpex.HTML.CoreComponents.icon
      name="hero-user-plus"
      class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-blue-600"
    />
    """
  end


  def confirm_label(_assigns), do: "确认解封"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "确定要解封选中的用户吗？"

  @impl Backpex.ItemAction
  def fields do
    [
      unban_reason: %{
        module: Backpex.Fields.Textarea,
        label: "解封原因",
        type: :string,
        required: true
      }
    ]
  end

  @required_fields ~w[unban_reason]a

  @impl Backpex.ItemAction
  def changeset(change, attrs, _metadata \\ []) do
    change
    |> cast(attrs, @required_fields)
    |> validate_required(@required_fields)
    |> validate_length(:unban_reason, min: 5, max: 500)
  end

  def can?(_assigns, item) do
    # 只有活跃的封禁记录才能解封
    case item do
      %{status: status} -> status == 1 or status == "active"
      %{is_active: is_active} -> is_active
      %{active: active} -> active
      _ -> true
    end
  end

  @impl Backpex.ItemAction
  def handle(socket, items, params) do
    Logger.info("🔓 [UNBAN_USER] handle called with #{length(items)} items")
    Logger.info("🔓 [UNBAN_USER] params: #{inspect(params)}")
    
    %{assigns: %{current_user: current_user}} = socket.assigns

    # 批量解封用户
    results = 
      items
      |> Enum.map(fn item ->
        Logger.info("🔓 [UNBAN_USER] Processing ban record #{item.id}")
        
        case unban_user(item, current_user.id, params.unban_reason) do
          {:ok, _} -> 
            Logger.info("🔓 [UNBAN_USER] Successfully unbanned user #{item.user_id}")
            {:ok, item.id}
          {:error, reason} -> 
            Logger.error("🔓 [UNBAN_USER] Failed to unban user #{item.user_id}: #{inspect(reason)}")
            {:error, {item.id, reason}}
        end
      end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    failed_count = Enum.count(results, fn {status, _} -> status == :error end)

    Logger.info("🔓 [UNBAN_USER] Results: #{success_count} success, #{failed_count} failed")

    socket = 
      if success_count > 0 do
        message = "成功解封 #{success_count} 个用户"
        message = if failed_count > 0 do
          message <> "，#{failed_count} 个失败"
        else
          message
        end

        socket
        |> Phoenix.LiveView.put_flash(:info, message)
      else
        socket
        |> Phoenix.LiveView.put_flash(:error, "解封用户失败，请检查选中的封禁记录")
      end

    {:ok, socket}
  end

  defp unban_user(ban_record, admin_id, unban_reason) do
    try do
      case Teen.BanSystem.unban_user(ban_record.id, admin_id, unban_reason) do
        {:ok, updated_ban} -> {:ok, updated_ban}
        {:error, reason} -> {:error, reason}
      end
    rescue
      error -> {:error, error}
    end
  end
end
