defmodule Teen.ItemActions.BatchRejectWithdrawals do
  @moduledoc """
  批量审核拒绝提现申请的项目操作
  """

  use Backpex.ItemAction

  import Phoenix.Component

  import Ecto.Changeset
  require Logger

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "批量审核拒绝"

  @impl Backpex.ItemAction
  def icon(assigns, _item) do
    ~H"""
    <Backpex.HTML.CoreComponents.icon
      name="hero-x-circle"
      class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-blue-600"
    />
    """
  end

  @impl Backpex.ItemAction
  def confirm_label(_assigns), do: "确认批量审核拒绝"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "确定要批量审核拒绝选中的提现申请吗？"

  @impl Backpex.ItemAction
  def fields do
    [
      feedback: %{
        module: Backpex.Fields.Textarea,
        label: "拒绝原因",
        type: :string,
        required: true
      }
    ]
  end

  @required_fields ~w[feedback]a

  @impl Backpex.ItemAction
  def changeset(change, attrs, _metadata \\ []) do
    change
    |> cast(attrs, @required_fields)
    |> validate_required(@required_fields)
    |> validate_length(:feedback, min: 5, max: 500)
  end

  def can?(_assigns, _item) do
    # 只有待审核状态的提现记录才能审核拒绝
    true
  end

  @impl Backpex.ItemAction
  def handle(socket, items, params) do
    Logger.info("💸 [BATCH_REJECT] handle called with #{length(items)} items")
    Logger.info("💸 [BATCH_REJECT] params: #{inspect(params)}")

    # 安全地获取 current_user
    current_user = case socket do
      %Phoenix.LiveView.Socket{assigns: %{current_user: user}} -> user
      %{current_user: user} -> user
      _ ->
        Logger.error("💸 [BATCH_REJECT] Cannot find current_user in socket")
        raise "Current user not found"
    end

    # 过滤出只有待审核状态的提现记录
    pending_withdrawals =
      items
      |> Enum.filter(fn withdrawal -> withdrawal.audit_status == 0 end)

    Logger.info("💸 [BATCH_REJECT] Found #{length(pending_withdrawals)} pending withdrawals")

    if length(pending_withdrawals) == 0 do
      socket =
        socket
        |> Phoenix.LiveView.put_flash(:error, "没有待审核的提现申请")

      {:ok, socket}
    else
      # 批量审核拒绝
      results =
        pending_withdrawals
        |> Enum.map(fn withdrawal ->
          Logger.info("💸 [BATCH_REJECT] Processing withdrawal #{withdrawal.id}")

          case Teen.Services.WithdrawalService.reject_withdrawal(
            withdrawal.id,
            current_user.id,
            params.feedback
          ) do
            {:ok, _} ->
              Logger.info("💸 [BATCH_REJECT] Successfully rejected withdrawal #{withdrawal.id}")
              {:ok, withdrawal.id}
            {:error, reason} ->
              Logger.error("💸 [BATCH_REJECT] Failed to reject withdrawal #{withdrawal.id}: #{inspect(reason)}")
              {:error, {withdrawal.id, reason}}
          end
        end)

      success_count = Enum.count(results, fn {status, _} -> status == :ok end)
      failed_count = Enum.count(results, fn {status, _} -> status == :error end)

      Logger.info("💸 [BATCH_REJECT] Results: #{success_count} success, #{failed_count} failed")

      socket =
        if success_count > 0 do
          message = "成功拒绝 #{success_count} 个提现申请"
          message = if failed_count > 0 do
            message <> "，#{failed_count} 个失败"
          else
            message
          end

          socket
          |> Phoenix.LiveView.put_flash(:info, message)
        else
          socket
          |> Phoenix.LiveView.put_flash(:error, "批量拒绝失败，请检查选中的提现记录")
        end

      {:ok, socket}
    end
  end
end
