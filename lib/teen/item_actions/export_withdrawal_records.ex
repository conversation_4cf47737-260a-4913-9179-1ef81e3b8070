defmodule Teen.ItemActions.ExportWithdrawalRecords do
  @moduledoc """
  导出提现记录的项目操作
  """

  use Backpex.ItemAction

  import Phoenix.Component

  require Logger

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "导出记录"

  @impl Backpex.ItemAction
  def icon(assigns, _item) do
    ~H"""
    <Backpex.HTML.CoreComponents.icon
      name="hero-arrow-down-tray"
      class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-blue-600"
    />
    """
  end


  @impl Backpex.ItemAction
  def confirm_label(_assigns), do: "确认导出"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "确定要导出选中的提现记录吗？"

  @impl Backpex.ItemAction
  def fields, do: []

  def can?(_assigns, _item) do
    # 所有提现记录都可以导出
    true
  end

  @impl Backpex.ItemAction
  def handle(socket, items, _params) do
    Logger.info("📊 [EXPORT_WITHDRAWAL] handle called with #{length(items)} items")

    case export_withdrawal_records(items) do
      {:ok, file_path} ->
        Logger.info("📊 [EXPORT_WITHDRAWAL] Successfully exported #{length(items)} records to #{file_path}")

        socket =
          socket
          |> Phoenix.LiveView.put_flash(:info, "成功导出 #{length(items)} 条提现记录")
          |> Phoenix.LiveView.push_event("download", %{
            url: "/downloads/#{Path.basename(file_path)}",
            filename: Path.basename(file_path)
          })

        {:ok, socket}

      {:error, reason} ->
        Logger.error("📊 [EXPORT_WITHDRAWAL] Failed to export records: #{inspect(reason)}")

        socket =
          socket
          |> Phoenix.LiveView.put_flash(:error, "导出提现记录失败：#{inspect(reason)}")

        {:ok, socket}
    end
  end

  defp export_withdrawal_records(records) do
    try do
      # 生成 CSV 内容
      csv_content = generate_csv_content(records)

      # 生成文件名
      timestamp = DateTime.utc_now() |> DateTime.to_unix()
      filename = "withdrawal_records_#{timestamp}.csv"
      file_path = Path.join([System.tmp_dir(), filename])

      # 写入文件
      case File.write(file_path, csv_content) do
        :ok -> {:ok, file_path}
        {:error, reason} -> {:error, reason}
      end
    rescue
      error -> {:error, error}
    end
  end

  defp generate_csv_content(records) do
    # CSV 头部
    headers = [
      "ID",
      "用户ID",
      "提现金额",
      "实际金额",
      "手续费",
      "支付方式",
      "银行信息",
      "审核状态",
      "进度状态",
      "审核员ID",
      "审核时间",
      "反馈信息",
      "创建时间",
      "更新时间"
    ]

    # 转换记录为 CSV 行
    rows = Enum.map(records, &record_to_csv_row/1)

    # 组合头部和数据行
    all_rows = [headers | rows]

    # 生成 CSV 字符串
    all_rows
    |> Enum.map(&Enum.join(&1, ","))
    |> Enum.join("\n")
  end

  defp record_to_csv_row(record) do
    [
      to_string(record.id),
      to_string(record.user_id),
      to_string(record.withdrawal_amount),
      to_string(record.actual_amount),
      to_string(record.fee_amount),
      to_string(record.payment_method || ""),
      to_string(record.bank_info || ""),
      audit_status_to_string(record.audit_status),
      progress_status_to_string(record.progress_status),
      to_string(record.auditor_id || ""),
      to_string(record.audit_time || ""),
      to_string(record.feedback || ""),
      to_string(record.created_at),
      to_string(record.updated_at)
    ]
  end

  defp audit_status_to_string(0), do: "待审核"
  defp audit_status_to_string(1), do: "审核通过"
  defp audit_status_to_string(2), do: "审核拒绝"
  defp audit_status_to_string(_), do: "未知"

  defp progress_status_to_string(0), do: "排队中"
  defp progress_status_to_string(1), do: "处理中"
  defp progress_status_to_string(2), do: "支付成功"
  defp progress_status_to_string(3), do: "支付失败"
  defp progress_status_to_string(4), do: "人工处理"
  defp progress_status_to_string(5), do: "已取消"
  defp progress_status_to_string(_), do: "未知"
end
