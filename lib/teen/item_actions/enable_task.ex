defmodule Teen.ItemActions.EnableTask do
  @moduledoc """
  启用任务的项目操作
  """

  use Backpex.ItemAction

  import Phoenix.Component

  require Logger

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "启用任务"

  @impl Backpex.ItemAction
  def icon(assigns, _item) do
    ~H"""
    <Backpex.HTML.CoreComponents.icon
      name="hero-check-circle"
      class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-blue-600"
    />
    """
  end


  def confirm_label(_assigns), do: "确认启用"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "确定要启用选中的任务吗？"

  @impl Backpex.ItemAction
  def fields, do: []

  def can?(_assigns, item) do
    # 只有禁用状态的任务才能启用
    case item do
      %{status: status} -> status == 0 or status == false
      %{is_enabled: is_enabled} -> not is_enabled
      %{enabled: enabled} -> not enabled
      _ -> true
    end
  end

  @impl Backpex.ItemAction
  def handle(socket, items, _params) do
    Logger.info("🎯 [ENABLE_TASK] handle called with #{length(items)} items")
    
    # 批量启用任务
    results = 
      items
      |> Enum.map(fn item ->
        Logger.info("🎯 [ENABLE_TASK] Processing item #{item.id}")
        
        case update_task_status(item, true) do
          {:ok, _} -> 
            Logger.info("🎯 [ENABLE_TASK] Successfully enabled task #{item.id}")
            {:ok, item.id}
          {:error, reason} -> 
            Logger.error("🎯 [ENABLE_TASK] Failed to enable task #{item.id}: #{inspect(reason)}")
            {:error, {item.id, reason}}
        end
      end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    failed_count = Enum.count(results, fn {status, _} -> status == :error end)

    Logger.info("🎯 [ENABLE_TASK] Results: #{success_count} success, #{failed_count} failed")

    socket = 
      if success_count > 0 do
        message = "成功启用 #{success_count} 个任务"
        message = if failed_count > 0 do
          message <> "，#{failed_count} 个失败"
        else
          message
        end

        socket
        |> Phoenix.LiveView.put_flash(:info, message)
      else
        socket
        |> Phoenix.LiveView.put_flash(:error, "启用任务失败，请检查选中的任务")
      end

    {:ok, socket}
  end

  defp update_task_status(item, status) do
    # 根据不同的资源类型调用相应的更新方法
    case item.__struct__ do
      Teen.ActivitySystem.GameTask ->
        Teen.ActivitySystem.GameTask.enable_task(item)

      Teen.ActivitySystem.WeeklyCard ->
        Teen.ActivitySystem.WeeklyCard.enable_card(item)

      Teen.ActivitySystem.SevenDayTask ->
        Teen.ActivitySystem.SevenDayTask.enable_task(item)

      Teen.ActivitySystem.VipGift ->
        Teen.ActivitySystem.VipGift.enable_gift(item)

      Teen.ActivitySystem.RechargeTask ->
        Teen.ActivitySystem.RechargeTask.enable_task(item)

      Teen.ActivitySystem.RechargeWheel ->
        Teen.ActivitySystem.RechargeWheel.enable_wheel(item)

      Teen.ActivitySystem.ScratchCardActivity ->
        Teen.ActivitySystem.ScratchCardActivity.enable_activity(item)

      Teen.ActivitySystem.FirstRechargeGift ->
        Teen.ActivitySystem.FirstRechargeGift.enable_gift(item)

      Teen.ActivitySystem.LossRebateJar ->
        Teen.ActivitySystem.LossRebateJar.enable_jar(item)

      Teen.ActivitySystem.InviteCashActivity ->
        Teen.ActivitySystem.InviteCashActivity.enable_activity(item)

      Teen.ActivitySystem.BindingReward ->
        Teen.ActivitySystem.BindingReward.enable_reward(item)

      Teen.ActivitySystem.FreeBonusTask ->
        Teen.ActivitySystem.FreeBonusTask.enable_task(item)

      _ ->
        # 通用的状态更新方法
        update_attrs = case item do
          %{status: _} -> %{status: if(status, do: 1, else: 0)}
          %{is_enabled: _} -> %{is_enabled: status}
          %{enabled: _} -> %{enabled: status}
          _ -> %{active: status}
        end

        case item.__struct__.update(item, update_attrs) do
          {:ok, updated_item} -> {:ok, updated_item}
          {:error, changeset} -> {:error, changeset}
        end
    end
  end
end
