defmodule Teen.ItemActions.ResetScratchCard do
  @moduledoc """
  重置刮刮卡的项目操作
  """

  use Backpex.ItemAction

  import Phoenix.Component

  require Logger

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "重置刮刮卡"

  @impl Backpex.ItemAction
  def icon(assigns, _item) do
    ~H"""
    <Backpex.HTML.CoreComponents.icon
      name="hero-arrow-path"
      class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-blue-600"
    />
    """
  end


  def confirm_label(_assigns), do: "确认重置"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "确定要解除选中用户的刮刮卡限制吗？"

  @impl Backpex.ItemAction
  def fields, do: []

  def can?(_assigns, _item) do
    # 所有用户活动参与记录都可以重置刮刮卡
    true
  end

  @impl Backpex.ItemAction
  def handle(socket, items, _params) do
    Logger.info("🎫 [RESET_SCRATCH_CARD] handle called with #{length(items)} items")
    
    # 批量重置刮刮卡
    results = 
      items
      |> Enum.map(fn item ->
        Logger.info("🎫 [RESET_SCRATCH_CARD] Processing item #{item.id}")
        
        case reset_user_scratch_card(item) do
          {:ok, _} -> 
            Logger.info("🎫 [RESET_SCRATCH_CARD] Successfully reset scratch card for user #{item.user_id}")
            {:ok, item.id}
          {:error, reason} -> 
            Logger.error("🎫 [RESET_SCRATCH_CARD] Failed to reset scratch card for user #{item.user_id}: #{inspect(reason)}")
            {:error, {item.id, reason}}
        end
      end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    failed_count = Enum.count(results, fn {status, _} -> status == :error end)

    Logger.info("🎫 [RESET_SCRATCH_CARD] Results: #{success_count} success, #{failed_count} failed")

    socket = 
      if success_count > 0 do
        message = "成功重置 #{success_count} 个用户的刮刮卡限制"
        message = if failed_count > 0 do
          message <> "，#{failed_count} 个失败"
        else
          message
        end

        socket
        |> Phoenix.LiveView.put_flash(:info, message)
      else
        socket
        |> Phoenix.LiveView.put_flash(:error, "重置刮刮卡失败，请检查选中的用户")
      end

    {:ok, socket}
  end

  defp reset_user_scratch_card(participation) do
    try do
      # 获取用户ID
      user_id = case participation do
        %{user_id: user_id} -> user_id
        %{user: %{id: user_id}} -> user_id
        _ -> nil
      end

      if user_id do
        # 重置用户的刮刮卡相关状态
        case Teen.ActivitySystem.reset_user_scratch_card_limit(user_id) do
          {:ok, _} -> {:ok, :reset}
          {:error, reason} -> {:error, reason}
        end
      else
        {:error, :invalid_user}
      end
    rescue
      error -> {:error, error}
    end
  end
end
