defmodule Teen.ItemActions.DuplicateConfig do
  @moduledoc """
  复制配置的项目操作
  """

  use Backpex.ItemAction

  import Phoenix.Component

  require Logger

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "复制配置"

  @impl Backpex.ItemAction
  def icon(assigns, _item) do
    ~H"""
    <Backpex.HTML.CoreComponents.icon
      name="hero-document-duplicate"
      class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-blue-600"
    />
    """
  end


  def confirm_label(_assigns), do: "确认复制"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "确定要复制选中的配置吗？"

  @impl Backpex.ItemAction
  def fields, do: []

  def can?(_assigns, _item) do
    # 所有配置都可以复制
    true
  end

  @impl Backpex.ItemAction
  def handle(socket, items, _params) do
    Logger.info("🔧 [DUPLICATE_CONFIG] handle called with #{length(items)} items")
    
    # 批量复制配置
    results = 
      items
      |> Enum.map(fn item ->
        Logger.info("🔧 [DUPLICATE_CONFIG] Processing item #{item.id}")
        
        case duplicate_config(item) do
          {:ok, _} -> 
            Logger.info("🔧 [DUPLICATE_CONFIG] Successfully duplicated config #{item.id}")
            {:ok, item.id}
          {:error, reason} -> 
            Logger.error("🔧 [DUPLICATE_CONFIG] Failed to duplicate config #{item.id}: #{inspect(reason)}")
            {:error, {item.id, reason}}
        end
      end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    failed_count = Enum.count(results, fn {status, _} -> status == :error end)

    Logger.info("🔧 [DUPLICATE_CONFIG] Results: #{success_count} success, #{failed_count} failed")

    socket = 
      if success_count > 0 do
        message = "成功复制 #{success_count} 个配置"
        message = if failed_count > 0 do
          message <> "，#{failed_count} 个失败"
        else
          message
        end

        socket
        |> Phoenix.LiveView.put_flash(:info, message)
      else
        socket
        |> Phoenix.LiveView.put_flash(:error, "复制配置失败，请检查选中的配置")
      end

    {:ok, socket}
  end

  defp duplicate_config(item) do
    # 根据不同的资源类型调用相应的复制方法
    case item.__struct__ do
      Teen.PaymentSystem.PaymentConfig ->
        duplicate_payment_config(item)

      Teen.PaymentSystem.WithdrawalConfig ->
        duplicate_withdrawal_config(item)

      Teen.PaymentSystem.ExchangeConfig ->
        duplicate_exchange_config(item)

      _ ->
        {:error, :unsupported_resource}
    end
  end

  defp duplicate_payment_config(config) do
    # 创建配置的副本，修改名称和状态
    duplicate_attrs = 
      config
      |> Map.from_struct()
      |> Map.drop([:id, :inserted_at, :updated_at])
      |> Map.put(:name, "#{config.name} (副本)")
      |> Map.put(:status, 0)  # 新配置默认禁用

    Teen.PaymentSystem.PaymentConfig.create(duplicate_attrs)
  end

  defp duplicate_withdrawal_config(config) do
    # 创建配置的副本，修改名称和状态
    duplicate_attrs = 
      config
      |> Map.from_struct()
      |> Map.drop([:id, :inserted_at, :updated_at])
      |> Map.put(:name, "#{config.name} (副本)")
      |> Map.put(:status, 0)  # 新配置默认禁用

    Teen.PaymentSystem.WithdrawalConfig.create(duplicate_attrs)
  end

  defp duplicate_exchange_config(config) do
    # 创建配置的副本，修改名称和状态
    duplicate_attrs = 
      config
      |> Map.from_struct()
      |> Map.drop([:id, :inserted_at, :updated_at])
      |> Map.put(:name, "#{config.name} (副本)")
      |> Map.put(:status, 0)  # 新配置默认禁用

    Teen.PaymentSystem.ExchangeConfig.create(duplicate_attrs)
  end
end
