defmodule Teen.ItemActions.DisableBank do
  @moduledoc """
  禁用银行的项目操作
  """

  use Backpex.ItemAction

  import Phoenix.Component

  require Logger

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "禁用银行"

  @impl Backpex.ItemAction
  def icon(assigns, _item) do
    ~H"""
    <Backpex.HTML.CoreComponents.icon
      name="hero-x-circle"
      class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-blue-600"
    />
    """
  end


  def confirm_label(_assigns), do: "确认禁用"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "确定要禁用选中的银行吗？"

  @impl Backpex.ItemAction
  def fields, do: []

  def can?(_assigns, item) do
    # 只有启用状态的银行才能禁用
    case item do
      %{status: status} -> status == 1 or status == true
      %{is_enabled: is_enabled} -> is_enabled
      %{enabled: enabled} -> enabled
      _ -> true
    end
  end

  @impl Backpex.ItemAction
  def handle(socket, items, _params) do
    Logger.info("🏦 [DISABLE_BANK] handle called with #{length(items)} items")
    
    # 批量禁用银行
    results = 
      items
      |> Enum.map(fn item ->
        Logger.info("🏦 [DISABLE_BANK] Processing item #{item.id}")
        
        case update_bank_status(item, false) do
          {:ok, _} -> 
            Logger.info("🏦 [DISABLE_BANK] Successfully disabled bank #{item.id}")
            {:ok, item.id}
          {:error, reason} -> 
            Logger.error("🏦 [DISABLE_BANK] Failed to disable bank #{item.id}: #{inspect(reason)}")
            {:error, {item.id, reason}}
        end
      end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    failed_count = Enum.count(results, fn {status, _} -> status == :error end)

    Logger.info("🏦 [DISABLE_BANK] Results: #{success_count} success, #{failed_count} failed")

    socket = 
      if success_count > 0 do
        message = "成功禁用 #{success_count} 个银行"
        message = if failed_count > 0 do
          message <> "，#{failed_count} 个失败"
        else
          message
        end

        socket
        |> Phoenix.LiveView.put_flash(:info, message)
      else
        socket
        |> Phoenix.LiveView.put_flash(:error, "禁用银行失败，请检查选中的银行")
      end

    {:ok, socket}
  end

  defp update_bank_status(item, status) do
    # 根据不同的资源类型调用相应的更新方法
    case item.__struct__ do
      Teen.PaymentSystem.BankConfig ->
        Teen.PaymentSystem.BankConfig.disable(item)

      _ ->
        # 通用的状态更新方法
        update_attrs = case item do
          %{status: _} -> %{status: if(status, do: 1, else: 0)}
          %{is_enabled: _} -> %{is_enabled: status}
          %{enabled: _} -> %{enabled: status}
          _ -> %{active: status}
        end

        case item.__struct__.update(item, update_attrs) do
          {:ok, updated_item} -> {:ok, updated_item}
          {:error, changeset} -> {:error, changeset}
        end
    end
  end
end
