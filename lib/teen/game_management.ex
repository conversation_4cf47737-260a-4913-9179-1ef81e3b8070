defmodule Teen.GameManagement do
  @moduledoc """
  游戏管理域

  包含平台配置、游戏配置、服务器管理、机器人管理、渠道包管理、VIP等级管理等功能
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  import Ash.Query
  import Ash.Expr
  require Logger

  alias Teen.GameManagement.{Platform, RobotConfig, UserVipRecord, GameRecord}
  alias Teen.VipSystem.VipLevel
  alias Cypridina.Accounts.User

  admin do
    show? true
  end

  resources do
    resource Teen.GameManagement.ManageGameConfig
    resource Teen.GameManagement.LeveRoomConfig
    resource Teen.GameManagement.Platform
    resource Teen.GameManagement.UserVipRecord
    resource Teen.GameManagement.GameRecord
    resource Teen.Resources.Inventory.WalletControlConfig
    resource Teen.GameManagement.JackpotConfig
  end

  @doc """
  打印当前游戏和房间列表的详细日志
  """
  def print_game_lists_log() do
    Logger.info("=" <> String.duplicate("=", 79))
    Logger.info("🎯 [GAME_LISTS_LOG] 开始打印游戏管理系统状态")
    Logger.info("=" <> String.duplicate("=", 79))

    # 获取并打印站点游戏列表
    site_games = get_enabled_games()

    Logger.info("")
    Logger.info(String.duplicate("-", 40))

    # 获取并打印房间列表
    rooms = get_enabled_rooms()

    Logger.info("")
    Logger.info("=" <> String.duplicate("=", 79))
    Logger.info("📊 [SUMMARY] 游戏管理系统统计信息")
    Logger.info("📊 [SUMMARY] 启用的站点游戏总数: #{map_size(site_games)}")
    Logger.info("📊 [SUMMARY] 启用的房间总数: #{map_size(rooms)}")

    avg_rooms =
      if map_size(site_games) > 0 do
        Float.round(map_size(rooms) / map_size(site_games), 2)
      else
        0
      end

    Logger.info("📊 [SUMMARY] 平均每个游戏的房间数: #{avg_rooms}")
    Logger.info("=" <> String.duplicate("=", 79))

    %{
      site_games: site_games,
      rooms: rooms,
      stats: %{
        total_games: map_size(site_games),
        total_rooms: map_size(rooms),
        avg_rooms_per_game: avg_rooms
      }
    }
  end

  @doc """
  获取启用的游戏列表，用于生成sitegamelist1数据
  """
  def get_enabled_games() do
    # Logger.info("🎮 [GAME_MANAGEMENT] 开始获取启用的游戏列表...")

    raw_games = Teen.GameManagement.ManageGameConfig.list_enabled!()
    # Logger.info("🎮 [GAME_MANAGEMENT] 从数据库获取到 #{length(raw_games)} 个启用的游戏")

    # 打印原始游戏数据
    # Enum.each(raw_games, fn game ->
    #   Logger.info(
    #     "🎮 [RAW_GAME] ID: #{game.game_id}, 名称: #{game.game_name}, 状态: #{game.is_enabled}"
    #   )
    # end)

    formatted_games =
      raw_games
      |> Enum.map(&format_game_for_sitelist/1)
      |> Enum.with_index(1)
      |> Enum.into(%{}, fn {game, index} -> {to_string(index), game} end)

    # Logger.info("🎮 [GAME_MANAGEMENT] 站点游戏列表格式化完成")
    # Logger.info("📋 [SITEGAMELIST1] 最终站点游戏列表:")

    # 打印格式化后的站点游戏列表
    # Enum.each(formatted_games, fn {index, game} ->
    #   Logger.info(
    #     "📋 [SITEGAMELIST1] [#{index}] 游戏ID: #{game["gameid"]}, 名称: #{game["name"]}, 状态: #{game["status"]}, 模式: #{game["mode"]}"
    #   )
    # end)

    # Logger.info("✅ [GAME_MANAGEMENT] 站点游戏列表获取完成，总计: #{map_size(formatted_games)} 个游戏")

    formatted_games
  end

  @doc """
  获取启用的房间列表，用于生成gamelist数据
  """
  def get_enabled_rooms() do
    try do
      # 使用关联查询一次性获取启用的房间和对应的游戏配置
      enabled_rooms_data =
        Teen.GameManagement.LeveRoomConfig
        |> Ash.Query.filter(expr(is_enabled == true))
        |> Ash.Query.load(:game_config)
        |> Ash.read!()
        |> Enum.filter(fn room ->
          room.game_config && room.game_config.is_enabled
        end)

      enabled_rooms =
        enabled_rooms_data
        |> Enum.map(&format_room_for_gamelist/1)
        |> Enum.with_index(1)
        |> Enum.into(%{}, fn {room, index} -> {to_string(index), room} end)

      # Logger.info("✅ [ROOM_MANAGEMENT] 获取启用房间列表完成，总计: #{map_size(enabled_rooms)} 个房间")

      enabled_rooms
    rescue
      error ->
        # Logger.error("❌ [ROOM_MANAGEMENT] 获取启用房间列表失败: #{inspect(error)}")
        %{}
    end
  end

  @doc """
  根据游戏ID获取房间列表
  """
  def get_rooms_by_game_id(game_id) do
    Teen.GameManagement.LeveRoomConfig.get_by_game_id!(game_id: game_id)
    |> Enum.filter(& &1.is_enabled)
    |> Enum.map(&format_room_for_gamelist/1)
  end

  @doc """
  创建游戏配置及其相关配置
  """
  def create_game_with_configs(game_attrs, room_configs \\ []) do
    Ash.Changeset.for_create(Teen.GameManagement.ManageGameConfig, :create, game_attrs)
    |> Ash.create!()
    |> then(fn game_config ->
      # 创建房间配置
      rooms =
        Enum.map(room_configs, fn room_attrs ->
          room_attrs = Map.put(room_attrs, :game_config_id, game_config.id)

          Ash.Changeset.for_create(Teen.GameManagement.LeveRoomConfig, :create, room_attrs)
          |> Ash.create!()
        end)

      %{
        game_config: game_config,
        room_configs: rooms
      }
    end)
  end

  # 私有函数：格式化游戏数据为sitegamelist1格式
  defp format_game_for_sitelist(game_config) do
    %{
      "gameid" => game_config.game_id,
      "status" => game_config.status,
      "mode" => game_config.mode,
      "name" => game_config.game_name,
      "game_class_type" => game_config.game_class_type
    }
  end

  @doc """
  获取所有启用的游戏配置
  """
  def list_enabled_games do
    try do
      games = Teen.GameManagement.ManageGameConfig.list_enabled!()
      {:ok, games}
    rescue
      error ->
        Logger.error("获取启用游戏配置失败: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  获取所有启用的房间配置
  """
  def list_enabled_rooms do
    try do
      rooms = Teen.GameManagement.LeveRoomConfig.list_enabled!()
      {:ok, rooms}
    rescue
      error ->
        Logger.error("获取启用房间配置失败: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  根据游戏ID获取启用的房间配置
  """
  def list_enabled_rooms_by_game(game_id) do
    try do
      rooms = Teen.GameManagement.LeveRoomConfig.list_enabled_by_game!(game_id: game_id)
      {:ok, rooms}
    rescue
      error ->
        Logger.error("获取游戏#{game_id}的启用房间配置失败: #{inspect(error)}")
        {:error, error}
    end
  end

  # 格式化房间数据为gamelist格式
  def format_room_for_gamelist(room_config) do
    %{
      "gameid" => room_config.game_id,
      "serverid" => room_config.server_id,
      "port" => room_config.port,
      "ip" => room_config.server_ip,
      "orderid" => room_config.order_id,
      "difen" => room_config.min_bet,
      "money" => room_config.entry_fee,
      "dingfen" => room_config.max_bet,
      "gamemaxnum" => room_config.max_players,
      "bundleName" => room_config.bundle_name
    }
  end

  @doc """
  获取游戏类型名称
  """
  def get_game_class_type_name(game_class_type) do
    case game_class_type do
      1 -> "单人游戏"
      2 -> "多人游戏"
      3 -> "百人游戏"
      _ -> "未知类型"
    end
  end

  @doc """
  初始化默认游戏配置数据
  """
  def seed_default_games() do
    default_games = [
      %{
        game_id: 1,
        game_name: "teenpatti",
        display_name: "Teen Patti",
        status: 2,
        mode: "normal",
        icon_url: "hall/images/games/font_teenPatti",
        description: "Teen patti",
        display_order: 1,
        is_enabled: true,
        # 多人游戏 - Teen Patti支持多人对战
        game_class_type: 2
      },
      %{
        game_id: 22,
        game_name: "longhu",
        display_name: "Dragon Tiger",
        status: 2,
        mode: "normal",
        icon_url: "hall/images/games/icon_longhu_name",
        description: "DragonTiger",
        display_order: 2,
        is_enabled: true,
        # 百人游戏 - 龙虎斗支持大量玩家同时下注
        game_class_type: 3
      },
      %{
        game_id: 23,
        game_name: "crash",
        display_name: "Crash",
        status: 2,
        mode: "normal",
        icon_url: "hall/images/games/icon_crash_name",
        description: "Crash",
        display_order: 2,
        is_enabled: true,
        # 百人游戏 - 龙虎斗支持大量玩家同时下注
        game_class_type: 3
      },
      %{
        game_id: 40,
        game_name: "slot777",
        display_name: "Slot 777",
        status: 2,
        mode: "normal",
        icon_url: "hall/images/games/icon_slot777",
        description: "Slot 777",
        display_order: 3,
        is_enabled: true,
        # 单人游戏 - 老虎机是单人游戏
        game_class_type: 1
      },
      %{
        game_id: 41,
        game_name: "slotniu",
        display_name: "Slot Niu",
        status: 2,
        mode: "normal",
        icon_url: "hall/images/games/icon_slotniu",
        description: "Slot Niu",
        display_order: 4,
        is_enabled: true,
        # 单人游戏 - 老虎机是单人游戏
        game_class_type: 1
      },
      %{
        game_id: 42,
        game_name: "slotcat",
        display_name: "Slot Cat",
        status: 2,
        mode: "normal",
        icon_url: "hall/images/games/icon_slotcat",
        description: "Slot Cat",
        display_order: 5,
        is_enabled: true,
        # 单人游戏 - 老虎机是单人游戏
        game_class_type: 1
      },
      %{
        game_id: 21,
        game_name: "jhandi_munda",
        display_name: "Jhandi Munda",
        status: 2,
        mode: "normal",
        icon_url: "hall/images/games/icon_jhandi_munda",
        description: "Jhandi Munda",
        display_order: 6,
        is_enabled: true,
        # 百人游戏 - Jhandi Munda支持大量玩家同时下注
        game_class_type: 3
      },
      %{
        game_id: 3,
        game_name: "pot_blind",
        display_name: "Pot Blind",
        status: 2,
        mode: "normal",
        icon_url: "hall/images/games/icon_pot_blind",
        description: "Pot Blind",
        display_order: 7,
        is_enabled: true,
        # 多人游戏 - Pot Blind支持多人对战
        game_class_type: 2
      },
      %{
        game_id: 54,
        game_name: "safariofwealth",
        display_name: "Safari of Wealth",
        status: 2,
        mode: "normal",
        icon_url: "hall/images/games/icon_safariofwealth",
        description: "Safari of Wealth",
        display_order: 8,
        is_enabled: true,
        # 单人游戏 - 老虎机是单人游戏
        game_class_type: 1
      }
    ]

    Enum.each(default_games, fn game_attrs ->
      case Teen.GameManagement.ManageGameConfig.get_by_game_id(game_attrs.game_id) do
        {:error, _} ->
          IO.puts("创建游戏配置: #{game_attrs.game_name}")
          IO.inspect(game_attrs, label: "游戏属性")

          case Teen.GameManagement.ManageGameConfig.create(game_attrs) do
            {:ok, game_config} ->
              IO.puts("✅ 成功创建游戏配置: #{game_attrs.game_name}")

            {:error, error} ->
              IO.puts("❌ 创建游戏配置失败: #{game_attrs.game_name}")
              IO.inspect(error, label: "错误详情")
          end

        {:ok, _} ->
          IO.puts("游戏配置已存在: #{game_attrs.game_name}")
          :already_exists
      end
    end)

    IO.puts("默认游戏配置初始化完成")
  end

  @doc """
  初始化默认VIP等级数据
  """
  def seed_default_vip_levels do
    default_vip_levels = [
      %{
        level: 0,
        level_name: "普通用户",
        recharge_requirement: Decimal.new("0"),
        daily_bonus: Decimal.new("100"),
        exchange_rate_bonus: Decimal.new("0"),
        recharge_bonus: Decimal.new("0"),
        status: 1,
        icon_url: "vip/icons/vip_0.png",
        description: "普通用户，享受基础服务",
        privileges: ["基础游戏权限", "客服支持"]
      },
      %{
        level: 1,
        level_name: "青铜VIP",
        recharge_requirement: Decimal.new("10000"),
        daily_bonus: Decimal.new("500"),
        exchange_rate_bonus: Decimal.new("5"),
        recharge_bonus: Decimal.new("2"),
        status: 1,
        icon_url: "vip/icons/vip_1.png",
        description: "青铜VIP，享受更多特权",
        privileges: ["基础游戏权限", "客服支持", "每日奖励", "充值奖励"]
      },
      %{
        level: 2,
        level_name: "白银VIP",
        recharge_requirement: Decimal.new("50000"),
        daily_bonus: Decimal.new("1000"),
        exchange_rate_bonus: Decimal.new("10"),
        recharge_bonus: Decimal.new("5"),
        status: 1,
        icon_url: "vip/icons/vip_2.png",
        description: "白银VIP，享受更多特权和奖励",
        privileges: ["基础游戏权限", "客服支持", "每日奖励", "充值奖励", "专属客服"]
      },
      %{
        level: 3,
        level_name: "黄金VIP",
        recharge_requirement: Decimal.new("100000"),
        daily_bonus: Decimal.new("2000"),
        exchange_rate_bonus: Decimal.new("15"),
        recharge_bonus: Decimal.new("8"),
        status: 1,
        icon_url: "vip/icons/vip_3.png",
        description: "黄金VIP，享受高级特权",
        privileges: ["基础游戏权限", "客服支持", "每日奖励", "充值奖励", "专属客服", "高级房间"]
      },
      %{
        level: 4,
        level_name: "铂金VIP",
        recharge_requirement: Decimal.new("500000"),
        daily_bonus: Decimal.new("5000"),
        exchange_rate_bonus: Decimal.new("20"),
        recharge_bonus: Decimal.new("12"),
        status: 1,
        icon_url: "vip/icons/vip_4.png",
        description: "铂金VIP，享受顶级特权",
        privileges: ["基础游戏权限", "客服支持", "每日奖励", "充值奖励", "专属客服", "高级房间", "专属活动"]
      },
      %{
        level: 5,
        level_name: "钻石VIP",
        recharge_requirement: Decimal.new("1000000"),
        daily_bonus: Decimal.new("10000"),
        exchange_rate_bonus: Decimal.new("25"),
        recharge_bonus: Decimal.new("15"),
        status: 1,
        icon_url: "vip/icons/vip_5.png",
        description: "钻石VIP，享受至尊特权",
        privileges: ["基础游戏权限", "客服支持", "每日奖励", "充值奖励", "专属客服", "高级房间", "专属活动", "至尊服务"]
      }
    ]

    Enum.each(default_vip_levels, fn vip_attrs ->
      # 使用正确的参数格式调用 get_by_level
      case Teen.VipSystem.VipLevel.get_by_level(vip_attrs[:level]) do
        {:error, _} ->
          IO.puts("创建VIP等级: #{vip_attrs[:level_name]}")

          case Teen.VipSystem.VipLevel.create(vip_attrs) do
            {:ok, vip_level} ->
              IO.puts("✅ 成功创建VIP等级: #{vip_attrs[:level_name]}")

            {:error, error} ->
              IO.puts("❌ 创建VIP等级失败: #{vip_attrs[:level_name]}")
              IO.inspect(error, label: "错误详情")
          end

        {:ok, _} ->
          IO.puts("VIP等级已存在: #{vip_attrs[:level_name]}")
          :already_exists
      end
    end)

    IO.puts("默认VIP等级初始化完成")
  end

  @doc """
  初始化默认平台配置数据
  """
  def seed_default_platforms do
    default_platforms = [
      %{
        platform_number: "MAIN_001",
        platform_name: "主平台",
        platform_announcement: "欢迎来到我们的游戏平台！享受最佳的游戏体验。",
        agent_recharge_switch: 1,
        agent_platform_number: "AGENT_001",
        login_platform_number: "LOGIN_001",
        show_qr_code: 1,
        default_download_url: "https://download.example.com/app.apk",
        ios_download_url: "https://apps.apple.com/app/example",
        android_download_url: "https://download.example.com/android.apk",
        status: 1,
        sort_order: 1,
        config_data: %{
          "theme" => "default",
          "language" => "zh-CN",
          "currency" => "INR",
          "timezone" => "Asia/Shanghai"
        }
      },
      %{
        platform_number: "TEST_001",
        platform_name: "测试平台",
        platform_announcement: "这是测试平台，用于开发和测试。",
        agent_recharge_switch: 0,
        agent_platform_number: "AGENT_TEST",
        login_platform_number: "LOGIN_TEST",
        show_qr_code: 0,
        default_download_url: "https://test.example.com/app.apk",
        ios_download_url: "https://test.example.com/ios.ipa",
        android_download_url: "https://test.example.com/android.apk",
        status: 0,
        sort_order: 2,
        config_data: %{
          "theme" => "test",
          "language" => "en-US",
          "currency" => "USD",
          "timezone" => "UTC"
        }
      }
    ]

    Enum.each(default_platforms, fn platform_attrs ->
      case Teen.GameManagement.Platform.get_by_platform_number(%{
             platform_number: platform_attrs[:platform_number]
           }) do
        {:error, _} ->
          IO.puts("创建平台配置: #{platform_attrs[:platform_name]}")

          case Teen.GameManagement.Platform.create(platform_attrs) do
            {:ok, platform} ->
              IO.puts("✅ 成功创建平台配置: #{platform_attrs[:platform_name]}")

            {:error, error} ->
              IO.puts("❌ 创建平台配置失败: #{platform_attrs[:platform_name]}")
              IO.inspect(error, label: "错误详情")
          end

        {:ok, _} ->
          IO.puts("平台配置已存在: #{platform_attrs[:platform_name]}")
          :already_exists
      end
    end)

    IO.puts("默认平台配置初始化完成")
  end

  @doc """
  初始化所有默认配置数据
  """
  def seed_all_defaults do
    IO.puts("=" <> String.duplicate("=", 60))
    IO.puts("🚀 开始初始化所有默认配置数据...")
    IO.puts("=" <> String.duplicate("=", 60))

    try do
      seed_basic_defaults()

      # 3. 初始化VIP等级
      IO.puts("\n💎 [3/4] 初始化VIP等级...")
      seed_default_vip_levels()

      # 4. 初始化平台配置
      IO.puts("\n🌐 [4/4] 初始化平台配置...")
      seed_default_platforms()

      IO.puts("\n" <> "=" <> String.duplicate("=", 60))
      IO.puts("✅ 所有默认配置初始化完成！")
      IO.puts("📊 已完成：游戏配置、房间配置、VIP等级、平台配置")
      IO.puts("=" <> String.duplicate("=", 60))

      {:ok, :completed}
    rescue
      error ->
        IO.puts("\n❌ 初始化过程中发生错误:")
        IO.inspect(error, label: "错误详情")
        {:error, error}
    end
  end

  @doc """
  获取或创建用户VIP记录
  """
  def get_or_create_user_vip_record(user_id) do
    case UserVipRecord.get_by_user_id(user_id) do
      {:ok, record} ->
        {:ok, record}

      {:error, %Ash.Error.Query.NotFound{}} ->
        UserVipRecord.create(%{user_id: user_id})

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  更新用户充值记录并计算VIP等级
  """
  def update_user_recharge(user_id, recharge_amount) do
    with {:ok, record} <- get_or_create_user_vip_record(user_id),
         {:ok, updated_record} <-
           UserVipRecord.update_recharge_total(record, %{recharge_amount: recharge_amount}) do
      # 检查是否需要升级VIP等级
      check_and_update_vip_level(updated_record)
    end
  end

  @doc """
  更新用户奖励记录
  """
  def update_user_rewards(user_id, reward_amount) do
    with {:ok, record} <- get_or_create_user_vip_record(user_id) do
      UserVipRecord.update_rewards_total(record, %{reward_amount: reward_amount})
    end
  end

  @doc """
  更新用户VIP经验值
  """
  def update_user_vip_experience(user_id, experience_amount, operation \\ :add) do
    with {:ok, record} <- get_or_create_user_vip_record(user_id),
         {:ok, updated_record} <-
           UserVipRecord.update_vip_experience(record, %{
             experience_amount: experience_amount,
             operation: operation
           }) do
      # 检查是否需要升级VIP等级
      check_and_update_vip_level(updated_record)
    end
  end

  @doc """
  检查并更新用户VIP等级
  """
  def check_and_update_vip_level(vip_record) do
    current_level = vip_record.current_vip_level
    total_recharge = vip_record.total_recharge_amount

    case calculate_user_vip_level(total_recharge) do
      {:ok, new_level} when new_level > current_level ->
        case UserVipRecord.level_up(vip_record, %{new_level: new_level}) do
          {:ok, updated_record} ->
            {:ok,
             %{
               record: updated_record,
               level_changed: true,
               old_level: current_level,
               new_level: new_level
             }}

          {:error, reason} ->
            {:error, reason}
        end

      {:ok, _level} ->
        {:ok, %{record: vip_record, level_changed: false}}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取用户VIP统计信息
  """
  def get_user_vip_statistics(user_id) do
    case UserVipRecord.get_user_vip_stats(user_id: user_id) do
      {:ok, record} ->
        {:ok,
         %{
           current_level: record.current_vip_level,
           vip_experience: record.vip_experience,
           total_recharge: record.total_recharge_amount,
           total_rewards: record.total_rewards_received,
           level_up_count: record.level_up_count,
           daily_login_streak: record.daily_login_streak,
           last_level_up_at: record.last_level_up_at,
           last_recharge_at: record.last_recharge_at,
           last_reward_at: record.last_reward_at,
           monthly_recharge: record.monthly_recharge_amount,
           monthly_rewards: record.monthly_rewards_received
         }}

      {:error, %Ash.Error.Query.NotFound{}} ->
        {:ok,
         %{
           current_level: 0,
           vip_experience: Decimal.new("0"),
           total_recharge: Decimal.new("0"),
           total_rewards: Decimal.new("0"),
           level_up_count: 0,
           daily_login_streak: 0,
           last_level_up_at: nil,
           last_recharge_at: nil,
           last_reward_at: nil,
           monthly_recharge: Decimal.new("0"),
           monthly_rewards: Decimal.new("0")
         }}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  重置用户月度统计数据
  """
  def reset_monthly_statistics(user_id) do
    with {:ok, record} <- get_or_create_user_vip_record(user_id) do
      UserVipRecord.update(record, %{
        monthly_recharge_amount: Decimal.new("0"),
        monthly_rewards_received: Decimal.new("0")
      })
    end
  end

  @doc """
  批量重置所有用户月度统计数据
  """
  def reset_all_monthly_statistics do
    case UserVipRecord.read() do
      {:ok, records} ->
        results =
          Enum.map(records, fn record ->
            UserVipRecord.update(record, %{
              monthly_recharge_amount: Decimal.new("0"),
              monthly_rewards_received: Decimal.new("0")
            })
          end)

        {successes, failures} =
          Enum.split_with(results, fn
            {:ok, _} -> true
            _ -> false
          end)

        %{
          success_count: length(successes),
          failure_count: length(failures),
          failures: failures
        }

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  检查用户是否可以领取每日VIP奖励
  """
  def can_claim_daily_vip_bonus?(user_id) do
    case get_or_create_user_vip_record(user_id) do
      {:ok, record} ->
        today = Date.utc_today()
        last_bonus_date = record.last_daily_bonus_at

        case last_bonus_date do
          nil -> {:ok, true}
          ^today -> {:ok, false}
          _ -> {:ok, true}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  领取每日VIP奖励
  """
  def claim_daily_vip_bonus(user_id) do
    with {:ok, true} <- can_claim_daily_vip_bonus?(user_id),
         {:ok, bonus_amount} <- calculate_vip_daily_bonus(user_id),
         {:ok, record} <- get_or_create_user_vip_record(user_id) do
      # 更新记录
      case UserVipRecord.update(record, %{
             last_daily_bonus_at: Date.utc_today(),
             daily_login_streak: record.daily_login_streak + 1
           }) do
        {:ok, updated_record} ->
          {:ok,
           %{
             record: updated_record,
             bonus_amount: bonus_amount,
             streak: updated_record.daily_login_streak
           }}

        {:error, reason} ->
          {:error, reason}
      end
    else
      {:ok, false} -> {:error, "Already claimed today"}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  初始化基础配置数据（游戏配置和基础房间配置）
  """
  def seed_basic_defaults do
    IO.puts("=" <> String.duplicate("=", 60))
    IO.puts("🚀 开始初始化基础配置数据...")
    IO.puts("=" <> String.duplicate("=", 60))

    try do
      # 1. 初始化游戏配置
      IO.puts("\n📋 [1/2] 初始化游戏配置...")
      seed_default_games()

      # 2. 初始化基础房间配置
      IO.puts("\n🏠 [2/2] 初始化基础房间配置...")
      seed_basic_rooms()

      IO.puts("\n" <> "=" <> String.duplicate("=", 60))
      IO.puts("✅ 基础配置初始化完成！")
      IO.puts("📊 已完成：游戏配置、基础房间配置")
      IO.puts("=" <> String.duplicate("=", 60))

      {:ok, :completed}
    rescue
      error ->
        IO.puts("\n❌ 初始化过程中发生错误:")
        IO.inspect(error, label: "错误详情")
        {:error, error}
    end

    IO.puts("🏠 基础房间配置初始化完成")
  end

  @doc """
  初始化基础房间配置 - 为主要游戏创建房间配置
  """
  def seed_basic_rooms do
    IO.puts("🏠 开始初始化基础房间配置...")

    # 获取游戏配置映射
    case list_enabled_games() do
      {:ok, games} ->
        game_id_to_config_id = Map.new(games, &{&1.game_id, &1.id})

        # 获取现有房间配置
        case Teen.GameManagement.LeveRoomConfig.list_enabled() do
          {:ok, existing_room_configs} ->
            existing_room_keys =
              MapSet.new(existing_room_configs, fn room -> {room.game_id, room.server_id} end)

            IO.puts("📋 发现已存在的房间配置: #{inspect(MapSet.to_list(existing_room_keys))}")

            # 定义基础房间配置 - 为所有游戏创建房间配置（TeenPatti 有两个房间）
            basic_rooms = [
              # TeenPatti 房间 1
              %{
                game_config_id: game_id_to_config_id[1],
                game_id: 1,
                server_id: 1001,
                server_ip: "127.0.0.1",
                port: 4000,
                order_id: 1,
                min_bet: 10,
                max_bet: 0,
                entry_fee: 100,
                max_players: 6,
                bundle_name: "teenpatti",
                is_enabled: true
              },
              # TeenPatti 房间 2
              %{
                game_config_id: game_id_to_config_id[1],
                game_id: 1,
                server_id: 1002,
                server_ip: "127.0.0.1",
                port: 4000,
                order_id: 2,
                min_bet: 20,
                max_bet: 0,
                entry_fee: 200,
                max_players: 6,
                bundle_name: "teenpatti",
                is_enabled: true
              },
              # PotBlind 房间
              %{
                game_config_id: game_id_to_config_id[3],
                game_id: 3,
                server_id: 3001,
                server_ip: "127.0.0.1",
                port: 4000,
                order_id: 1,
                min_bet: 15,
                max_bet: 0,
                entry_fee: 150,
                max_players: 6,
                bundle_name: "pot_blind",
                is_enabled: true
              },
              # Jhandi Munda 房间
              %{
                game_config_id: game_id_to_config_id[21],
                game_id: 21,
                server_id: 2101,
                server_ip: "127.0.0.1",
                port: 4000,
                order_id: 1,
                min_bet: 20,
                max_bet: 0,
                entry_fee: 200,
                max_players: 8,
                bundle_name: "jhandi_munda",
                is_enabled: true
              },
              # DragonTiger 房间
              %{
                game_config_id: game_id_to_config_id[22],
                game_id: 22,
                server_id: 2201,
                server_ip: "127.0.0.1",
                port: 4000,
                order_id: 1,
                min_bet: 20,
                max_bet: 0,
                entry_fee: 200,
                max_players: 8,
                bundle_name: "longhu",
                is_enabled: true
              },
              # Slot777 房间
              %{
                game_config_id: game_id_to_config_id[40],
                game_id: 40,
                server_id: 4001,
                server_ip: "127.0.0.1",
                port: 4000,
                order_id: 1,
                min_bet: 100,
                max_bet: 0,
                entry_fee: 200,
                max_players: 1,
                bundle_name: "slot777",
                is_enabled: true
              },
              # SlotNiu 房间
              %{
                game_config_id: game_id_to_config_id[41],
                game_id: 41,
                server_id: 4101,
                server_ip: "127.0.0.1",
                port: 4000,
                order_id: 1,
                min_bet: 100,
                max_bet: 0,
                entry_fee: 200,
                max_players: 1,
                bundle_name: "slotniu",
                is_enabled: true
              },
              # SlotCat 房间
              %{
                game_config_id: game_id_to_config_id[42],
                game_id: 42,
                server_id: 4201,
                server_ip: "127.0.0.1",
                port: 4000,
                order_id: 1,
                min_bet: 100,
                max_bet: 0,
                entry_fee: 1000,
                max_players: 1,
                bundle_name: "slotcat",
                is_enabled: true
              },
              # Crash 房间
              %{
                game_config_id: game_id_to_config_id[23],
                game_id: 23,
                server_id: 2301,
                server_ip: "127.0.0.1",
                port: 4000,
                order_id: 1,
                min_bet: 500,
                max_bet: 0,
                entry_fee: 1000,
                max_players: 100,
                bundle_name: "crash",
                is_enabled: true
              },
              # SafariOfWealth 房间
              %{
                game_config_id: game_id_to_config_id[54],
                game_id: 54,
                server_id: 5401,
                server_ip: "127.0.0.1",
                port: 4000,
                order_id: 1,
                min_bet: 50,
                max_bet: 0,
                entry_fee: 500,
                max_players: 1,
                bundle_name: "safariofwealth",
                is_enabled: true
              }
            ]

            # 创建房间配置 - 只创建不存在的
            created_count = 0
            skipped_count = 0

            Enum.each(basic_rooms, fn room_attrs ->
              if room_attrs.game_config_id do
                room_key = {room_attrs.game_id, room_attrs.server_id}

                if MapSet.member?(existing_room_keys, room_key) do
                  IO.puts(
                    "⚠️  跳过房间配置 #{room_attrs.bundle_name} (游戏ID: #{room_attrs.game_id}, 服务器ID: #{room_attrs.server_id}) - 已存在"
                  )

                  skipped_count = skipped_count + 1
                else
                  case Teen.GameManagement.LeveRoomConfig.create(room_attrs) do
                    {:ok, room_config} ->
                      IO.puts(
                        "✅ 创建房间配置: #{room_config.bundle_name} (游戏ID: #{room_config.game_id}, 服务器ID: #{room_config.server_id})"
                      )

                      created_count = created_count + 1

                    {:error, reason} ->
                      IO.puts("❌ 创建房间配置失败: #{inspect(reason)}")
                  end
                end
              else
                IO.puts(
                  "⚠️  跳过房间配置 (游戏ID: #{room_attrs.game_id}, 服务器ID: #{room_attrs.server_id}) - 未找到对应的游戏配置"
                )

                skipped_count = skipped_count + 1
              end
            end)

            IO.puts("📊 房间配置创建完成 - 成功: #{created_count}, 跳过: #{skipped_count}")

          {:error, reason} ->
            IO.puts("❌ 获取房间配置失败: #{inspect(reason)}")
        end

      {:error, reason} ->
        IO.puts("❌ 获取游戏配置失败: #{inspect(reason)}")
    end

    IO.puts("🏠 基础房间配置初始化完成")
  end

  # 私有函数：根据充值金额计算VIP等级
  defp calculate_user_vip_level(total_recharge) do
    case VipLevel.get_level_by_recharge_amount(total_recharge) do
      {:ok, vip_level} -> {:ok, vip_level.level}
      {:error, reason} -> {:error, reason}
    end
  end

  # 私有函数：计算VIP每日奖励
  defp calculate_vip_daily_bonus(user_id) do
    case get_user_vip_statistics(user_id) do
      {:ok, stats} ->
        case VipLevel.get_by_level(stats.current_level) do
          {:ok, vip_level} -> {:ok, vip_level.daily_bonus}
          {:error, reason} -> {:error, reason}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end
end
