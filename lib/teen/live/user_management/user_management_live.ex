defmodule Teen.Live.UserManagement.UserManagementLive do
  @moduledoc """
  用户管理后台界面
  提供用户信息的查看、编辑、状态管理等功能
  """
  use CypridinaWeb, :live_view
  require Ash.Query
  alias Cypridina.Accounts.User
  alias <PERSON><PERSON>ridina.Accounts

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(:page_title, "用户管理")
     |> assign(:current_url, "/admin/users")
     |> assign(:search_term, "")
     |> assign(:selected_status, "all")
     |> assign(:selected_role, "all")
     |> assign(:show_modal, false)
     |> assign(:modal_action, nil)
     |> assign(:current_user_item, nil)
     |> assign(:fluid?, true)
     |> load_users(),
     layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "用户管理")
    |> assign(:current_user_item, nil)
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    user = get_user_by_id(id)

    socket
    |> assign(:page_title, "用户详情 - #{user.username}")
    |> assign(:current_user_item, user)
    |> assign(:show_modal, true)
    |> assign(:modal_action, :show)
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    user = get_user_by_id(id)

    socket
    |> assign(:page_title, "编辑用户 - #{user.username}")
    |> assign(:current_user_item, user)
    |> assign(:show_modal, true)
    |> assign(:modal_action, :edit)
  end

  @impl true
  def handle_event("search", %{"search" => %{"term" => term}}, socket) do
    {:noreply,
     socket
     |> assign(:search_term, term)
     |> load_users()}
  end

  def handle_event("filter_status", %{"status" => status}, socket) do
    {:noreply,
     socket
     |> assign(:selected_status, status)
     |> load_users()}
  end

  def handle_event("filter_role", %{"role" => role}, socket) do
    {:noreply,
     socket
     |> assign(:selected_role, role)
     |> load_users()}
  end

  def handle_event("close_modal", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_modal, false)
     |> assign(:modal_action, nil)
     |> assign(:current_user_item, nil)
     |> push_patch(to: ~p"/admin/users")}
  end

  def handle_event("toggle_user_status", %{"id" => id}, socket) do
    user = get_user_by_id(id)

    case toggle_user_active_status(user) do
      {:ok, _updated_user} ->
        {:noreply,
         socket
         |> put_flash(:info, "用户状态已更新")
         |> load_users()}

      {:error, _reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "更新用户状态失败")}
    end
  end

  def handle_event("reset_password", %{"id" => id}, socket) do
    user = get_user_by_id(id)

    case reset_user_password(user) do
      {:ok, _updated_user} ->
        {:noreply,
         socket
         |> put_flash(:info, "密码重置成功")
         |> load_users()}

      {:error, _reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "密码重置失败")}
    end
  end

  def handle_event("save_user", %{"user" => user_params}, socket) do
    case socket.assigns.modal_action do
      :edit ->
        case update_user(socket.assigns.current_user_item, user_params) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> put_flash(:info, "用户信息已更新")
             |> assign(:show_modal, false)
             |> assign(:modal_action, nil)
             |> assign(:current_user_item, nil)
             |> load_users()
             |> push_patch(to: ~p"/admin/users")}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> put_flash(:error, "更新用户信息失败")}
        end
    end
  end

  # 私有函数
  defp load_users(socket) do
    users = get_filtered_users(
      socket.assigns.search_term,
      socket.assigns.selected_status,
      socket.assigns.selected_role
    )

    assign(socket, :users, users)
  end

  defp get_filtered_users(search_term, status_filter, role_filter) do
    query = User
    |> Ash.Query.load([:profile])
    |> Ash.Query.sort(inserted_at: :desc)

    query = if search_term != "" do
      Ash.Query.filter(query, contains(username, ^search_term) or contains(email, ^search_term))
    else
      query
    end

    query = case status_filter do
      "active" -> Ash.Query.filter(query, confirmed_at != nil)
      "inactive" -> Ash.Query.filter(query, confirmed_at == nil)
      _ -> query
    end

    query = case role_filter do
      "admin" -> Ash.Query.filter(query, permission_level >= 1)
      "user" -> Ash.Query.filter(query, permission_level == 0)
      _ -> query
    end

    Ash.read!(query)
  end

  defp get_user_by_id(id) do
    User
    |> Ash.Query.filter(id == ^id)
    |> Ash.Query.load([:profile])
    |> Ash.read_one!()
  end

  defp toggle_user_active_status(user) do
    new_status = if user.confirmed_at, do: nil, else: DateTime.utc_now()

    user
    |> Ash.Changeset.for_update(:update, %{confirmed_at: new_status})
    |> Ash.update()
  end

  defp reset_user_password(user) do
    # 这里应该实现密码重置逻辑
    # 暂时返回成功
    {:ok, user}
  end

  defp update_user(user, params) do
    user
    |> Ash.Changeset.for_update(:update, params)
    |> Ash.update()
  end

  # 辅助函数
  defp format_user_status(user) do
    if user.confirmed_at do
      {"激活", "success"}
    else
      {"未激活", "warning"}
    end
  end

  defp format_user_role(user) do
    case user.permission_level do
      2 -> {"超级管理员", "error"}
      1 -> {"管理员", "info"}
      0 -> {"普通用户", "ghost"}
      _ -> {"未知", "ghost"}
    end
  end

  defp format_datetime(nil), do: "-"
  defp format_datetime(datetime) do
    datetime
    |> DateTime.shift_zone!("Asia/Shanghai")
    |> Calendar.strftime("%Y-%m-%d %H:%M:%S")
  end

  # 获取用户统计信息
  defp get_user_stats do
    total_users = User |> Ash.Query.count() |> Ash.read!()
    active_users = User |> Ash.Query.filter(confirmed_at != nil) |> Ash.Query.count() |> Ash.read!()
    admin_users = User |> Ash.Query.filter(permission_level >= 1) |> Ash.Query.count() |> Ash.read!()

    %{
      total: total_users,
      active: active_users,
      inactive: total_users - active_users,
      admins: admin_users
    }
  end
end
