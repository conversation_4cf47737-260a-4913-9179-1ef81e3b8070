defmodule Teen.Live.PaymentGatewayLive do
  @moduledoc """
  支付网关管理页面

  提供支付网关的创建、查看、编辑和管理功能
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.PaymentSystem.PaymentGateway
    ],
    layout: {Teen.Layouts, :admin},
    fluid?: true

  @impl Backpex.LiveResource
  def singular_name, do: "支付网关"

  @impl Backpex.LiveResource
  def plural_name, do: "支付网关"

  @impl Backpex.LiveResource
  def fields do
    %{
      name: %{
        module: Backpex.Fields.Text,
        label: "网关名称",
        searchable: true,
        help_text: "支付网关的显示名称"
      },
      gateway_type: %{
        module: Backpex.Fields.Select,
        label: "网关类型",
        options: [
          {"代收", "recharge"},
          {"代付", "withdraw"}
        ],
        searchable: true,
        help_text: "recharge-代收，withdraw-代付"
      },
      gateway_url: %{
        module: Backpex.Fields.Text,
        label: "网关URL",
        help_text: "支付网关的基础URL"
      },
      create_order_path: %{
        module: Backpex.Fields.Text,
        label: "下单接口路径",
        help_text: "创建订单的API路径"
      },
      query_order_path: %{
        module: Backpex.Fields.Text,
        label: "查询接口路径",
        help_text: "查询订单的API路径"
      },
      channel_id: %{
        module: Backpex.Fields.Text,
        label: "通道ID",
        help_text: "支付通道标识"
      },
      priority: %{
        module: Backpex.Fields.Number,
        label: "优先级",
        help_text: "数字越小优先级越高"
      },
      enabled: %{
        module: Backpex.Fields.Boolean,
        label: "是否启用",
        searchable: true
      },
      min_amount: %{
        module: Backpex.Fields.Number,
        label: "最小金额",
        help_text: "最小交易金额（元）"
      },
      max_amount: %{
        module: Backpex.Fields.Number,
        label: "最大金额",
        help_text: "最大交易金额（元）"
      },
      supported_currencies: %{
        module: Backpex.Fields.Text,
        label: "支持币种",
        help_text: "支持的货币类型，逗号分隔",
        only: [:index, :show],
        render: fn assigns ->
          value =
            case assigns.value do
              nil -> ""
              list when is_list(list) -> Enum.join(list, ", ")
              str when is_binary(str) -> str
              _ -> ""
            end

          assigns = assign(assigns, :display_value, value)

          ~H"""
          <span>{@display_value}</span>
          """
        end
      },
      timeout_seconds: %{
        module: Backpex.Fields.Number,
        label: "超时时间（秒）",
        help_text: "请求超时时间"
      },
      config_data: %{
        module: Backpex.Fields.Text,
        label: "配置数据",
        help_text: "额外的配置信息，JSON格式",
        only: [:index, :show],
        render: fn assigns ->
          value =
            case assigns.value do
              nil -> ""
              map when is_map(map) -> Jason.encode!(map, pretty: true)
              str when is_binary(str) -> str
              _ -> ""
            end

          assigns = assign(assigns, :formatted_value, value)

          ~H"""
          <pre class="text-sm bg-gray-100 p-2 rounded overflow-x-auto max-h-32"><%= @formatted_value %></pre>
          """
        end
      },
      last_test_at: %{
        module: Backpex.Fields.DateTime,
        label: "最后测试时间",
        only: [:index, :show],
        help_text: "最后一次连接测试的时间"
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "描述",
        help_text: "网关的详细描述信息"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        only: [:index, :show]
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        only: [:show]
      }
    }
  end

  def item_actions(_) do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        only: [:row, :index, :show]
      },
      enable_gateway: %{
        module: Teen.ItemActions.EnableGateway,
        only: [:index]
      },
      disable_gateway: %{
        module: Teen.ItemActions.DisableGateway,
        only: [:index]
      },
      test_connection: %{
        module: Teen.ItemActions.TestGatewayConnection,
        only: [:index]
      }
    ]
  end

  @impl Backpex.LiveResource
  def filters do
    []
  end
end
