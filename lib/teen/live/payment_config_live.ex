defmodule Teen.Live.PaymentConfigLive do
  @moduledoc """
  支付配置管理页面

  提供支付配置的创建、查看、编辑和管理功能
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.PaymentSystem.PaymentConfig
    ],
    layout: {Teen.Layouts, :admin},
    fluid?: true

  import Phoenix.Component

  @impl Backpex.LiveResource
  def singular_name, do: "支付配置"

  @impl Backpex.LiveResource
  def plural_name, do: "支付配置"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      :new -> true
      :edit -> true
      :delete -> true
      _ -> false
    end
  end

  @impl Backpex.LiveResource
  def fields do
    %{
      gateway: %{
        module: Backpex.Fields.BelongsTo,
        label: "支付网关",
        display_field: :name,
        live_resource: Teen.Live.PaymentGatewayLive,
        searchable: true,
        help_text: "选择对应的支付网关",
        # render: fn assigns ->
        #   case assigns.value do
        #     %Teen.PaymentSystem.PaymentGateway{} = gateway ->
        #       assigns = assign(assigns, :gateway_name, gateway.name || "未知网关")
        #       ~H"<%= @gateway_name %>"
        #     _ ->
        #       ~H"未设置"
        #   end
        # end
      },
      gateway_name: %{
        module: Backpex.Fields.Text,
        label: "网关名称",
        searchable: true,
        help_text: "支付网关的显示名称"
      },
      payment_type: %{
        module: Backpex.Fields.Select,
        label: "支付类型",
        options: Teen.Types.PaymentType.options(),
        searchable: true,
        help_text: "支付方式的类型标识"
      },
      payment_type_name: %{
        module: Backpex.Fields.Text,
        label: "支付类型名称",
        searchable: true,
        help_text: "支付方式的显示名称"
      },
      min_amount: %{
        module: Backpex.Fields.Number,
        label: "最小金额（分）",
        help_text: "单次支付的最小金额限制，单位：分"
      },
      max_amount: %{
        module: Backpex.Fields.Number,
        label: "最大金额（分）",
        help_text: "单次支付的最大金额限制，单位：分"
      },
      recharge_range: %{
        module: Backpex.Fields.Textarea,
        label: "充值范围",
        help_text: "可选的充值金额范围，JSON格式，例：[100, 500, 1000, 5000]",
        only: [:new, :edit, :show]
      },
      bonus_range: %{
        module: Backpex.Fields.Textarea,
        label: "奖励范围",
        help_text: "对应充值金额的奖励配置，JSON格式，例：{\"100\": 10, \"500\": 50}",
        only: [:new, :edit, :show]
      },
      fee_rate: %{
        module: Backpex.Fields.Number,
        label: "手续费率（%）",
        help_text: "支付手续费率，百分比形式（如3表示3%）"
      },
      deduction_rate: %{
        module: Backpex.Fields.Number,
        label: "扣除费率（%）",
        help_text: "额外扣除费率，百分比形式"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"禁用", 0},
          {"启用", 1}
        ],
        searchable: true
      },
      sort_order: %{
        module: Backpex.Fields.Number,
        label: "排序",
        help_text: "显示顺序，数字越小越靠前",
        default: fn _assigns -> 0 end
      },
      # config_data: %{
      #   module: Backpex.Fields.Textarea,
      #   label: "配置数据",
      #   help_text: "额外的配置信息，JSON格式",
      #   only: [:new, :edit, :show],
      #   # render: fn assigns ->
      #   #   value =
      #   #     case assigns.value do
      #   #       nil -> ""
      #   #       map when is_map(map) -> Jason.encode!(map, pretty: true)
      #   #       str when is_binary(str) -> str
      #   #       _ -> ""
      #   #     end

      #   #   assigns = assign(assigns, :formatted_value, value)

      #   #   ~H"""
      #   #   <pre class="text-sm bg-gray-100 p-2 rounded overflow-x-auto max-h-32"><%= @formatted_value %></pre>
      #   #   """
      #   # end
      # },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        only: [:index, :show]
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        only: [:show]
      }
    }
  end



  @impl Backpex.LiveResource
  def filters do
    [
      status: %{
        module: Teen.Filters.StatusSelect
      },
      payment_type: %{
        module: Teen.Filters.PaymentTypeSelect
      },
      gateway_id: %{
        module: Teen.Filters.GatewaySelect
      }
    ]
  end

  @impl Backpex.LiveResource
  def item_actions(_) do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        label: "查看详情",
        icon: "hero-eye"
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        label: "编辑",
        icon: "hero-pencil-square"
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        label: "删除",
        icon: "hero-trash",
        confirm_label: "确认删除",
        confirm_text: "确定要删除此支付配置吗？此操作不可撤销。"
      },
      enable_config: %{
        module: Teen.ItemActions.EnableConfig,
        only: [:index]
      },
      disable_config: %{
        module: Teen.ItemActions.DisableConfig,
        only: [:index]
      },
      duplicate_config: %{
        module: Teen.ItemActions.DuplicateConfig,
        only: [:index]
      }
    ]
  end

  @impl Backpex.LiveResource
  def ordering_options do
    [
      %{
        label: "排序（升序）",
        fields: [:sort_order]
      },
      %{
        label: "排序（降序）",
        fields: [sort_order: :desc]
      },
      %{
        label: "创建时间（最新）",
        fields: [inserted_at: :desc]
      },
      %{
        label: "创建时间（最早）",
        fields: [:inserted_at]
      },
      %{
        label: "网关名称",
        fields: [:gateway_name]
      },
      %{
        label: "支付类型",
        fields: [:payment_type]
      }
    ]
  end

  @impl Backpex.LiveResource
  def panel do
    [
      %{
        title: "支付配置管理",
        content: "管理各种支付方式的配置，包括费率、限额、状态等。支持多种支付类型：支付宝、微信、银行卡、数字货币等。"
      }
    ]
  end

  @impl Backpex.LiveResource
  def render_resource_slot(assigns, :index_header) do
    ~H"""
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-lg font-medium text-gray-900">支付渠道配置</h2>
          <p class="mt-1 text-sm text-gray-500">
            管理支付网关和支付方式的配置，包括费率设置、金额限制等
          </p>
        </div>
        <div class="flex space-x-3">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            {length(@items)} 个配置
          </span>
        </div>
      </div>
    </div>
    """
  end

  @impl Backpex.LiveResource
  def render_resource_slot(assigns, :index_footer) do
    ~H"""
    <div class="bg-gray-50 px-6 py-3 text-sm text-gray-500">
      <p>💡 提示：可以通过筛选器快速查找特定类型的支付配置，支持按状态、支付类型、网关等条件筛选。</p>
    </div>
    """
  end

  @impl Backpex.LiveResource
  def render_resource_slot(_assigns, _slot), do: nil
end
