defmodule Teen.Live.WithdrawalConfigLive do
  @moduledoc """
  提现配置管理页面

  提供提现配置的创建、查看、编辑和管理功能
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.PaymentSystem.WithdrawalConfig
    ],
    layout: {Teen.Layouts, :admin},
    fluid?: true

  @impl Backpex.LiveResource
  def singular_name, do: "提现配置"

  @impl Backpex.LiveResource
  def plural_name, do: "提现配置"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      :new -> true
      :edit -> true
      :delete -> true
      _ -> false
    end
  end

  @impl Backpex.LiveResource
  def fields do
    %{
      config_name: %{
        module: Backpex.Fields.Text,
        label: "配置名称",
        searchable: true,
        help_text: "提现配置的名称"
      },
      payment_method: %{
        module: Backpex.Fields.Select,
        label: "支付方式",
        options: [
          {"银行卡", "bank_card"},
          {"支付宝", "alipay"},
          {"UPI", "upi"}
        ],
        help_text: "支持的提现方式"
      },
      min_amount: %{
        module: Backpex.Fields.Number,
        label: "最小提现金额",
        help_text: "最小提现金额（分）",
        render: fn assigns ->
          amount = assigns.value || 0
          assigns = assign(assigns, :yuan_amount, Decimal.div(amount, 100) |> Decimal.to_string())

          ~H"""
          <span class="font-mono">¥{@yuan_amount}</span>
          """
        end
      },
      max_amount: %{
        module: Backpex.Fields.Number,
        label: "最大提现金额",
        help_text: "最大提现金额（分）",
        render: fn assigns ->
          amount = assigns.value || 0
          assigns = assign(assigns, :yuan_amount, Decimal.div(amount, 100) |> Decimal.to_string())

          ~H"""
          <span class="font-mono">¥{@yuan_amount}</span>
          """
        end
      },
      daily_limit: %{
        module: Backpex.Fields.Number,
        label: "每日限额",
        help_text: "每日提现限额（分）",
        render: fn assigns ->
          case assigns.value do
            nil ->
              ~H"""
              <span class="text-gray-500">无限制</span>
              """

            amount ->
              assigns =
                assign(assigns, :yuan_amount, Decimal.div(amount, 100) |> Decimal.to_string())

              ~H"""
              <span class="font-mono">¥{@yuan_amount}</span>
              """
          end
        end
      },
      fee_rate: %{
        module: Backpex.Fields.Number,
        label: "手续费率",
        help_text: "手续费率（%）",
        render: fn assigns ->
          rate = assigns.value || Decimal.new("0")
          assigns = assign(assigns, :rate_str, Decimal.to_string(rate))

          ~H"""
          <span class="font-mono">{@rate_str}%</span>
          """
        end
      },
      tax_rate: %{
        module: Backpex.Fields.Number,
        label: "税率",
        help_text: "税率（%）",
        render: fn assigns ->
          rate = assigns.value || Decimal.new("0")
          assigns = assign(assigns, :rate_str, Decimal.to_string(rate))

          ~H"""
          <span class="font-mono">{@rate_str}%</span>
          """
        end
      },
      processing_time_hours: %{
        module: Backpex.Fields.Number,
        label: "处理时间",
        help_text: "预计处理时间（小时）",
        render: fn assigns ->
          hours = assigns.value || 24
          assigns = assign(assigns, :hours, hours)

          ~H"""
          <span class="badge badge-info">{@hours}小时</span>
          """
        end
      },
      auto_approve_limit: %{
        module: Backpex.Fields.Number,
        label: "自动审核限额",
        help_text: "超过此金额需人工审核（分）",
        render: fn assigns ->
          case assigns.value do
            nil ->
              ~H"""
              <span class="text-gray-500">全部人工审核</span>
              """

            amount ->
              assigns =
                assign(assigns, :yuan_amount, Decimal.div(amount, 100) |> Decimal.to_string())

              ~H"""
              <span class="font-mono">¥{@yuan_amount}</span>
              """
          end
        end
      },
      vip_level_required: %{
        module: Backpex.Fields.Number,
        label: "所需VIP等级",
        help_text: "使用此配置所需的最低VIP等级",
        render: fn assigns ->
          level = assigns.value || 0
          assigns = assign(assigns, :level, level)

          ~H"""
          <span class="badge badge-secondary">VIP{@level}</span>
          """
        end
      },
      turnover_multiplier: %{
        module: Backpex.Fields.Number,
        label: "流水倍数",
        help_text: "流水倍数要求",
        render: fn assigns ->
          multiplier = assigns.value || Decimal.new("1")
          assigns = assign(assigns, :multiplier_str, Decimal.to_string(multiplier))

          ~H"""
          <span class="font-mono">{@multiplier_str}倍</span>
          """
        end
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"启用", 1},
          {"禁用", 0}
        ],
        render: fn assigns ->
          case assigns.value do
            1 ->
              ~H"""
              <span class="badge badge-success">启用</span>
              """

            0 ->
              ~H"""
              <span class="badge badge-error">禁用</span>
              """

            _ ->
              ~H"""
              <span class="badge badge-ghost">未知</span>
              """
          end
        end
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "配置描述",
        help_text: "配置的详细说明"
      },
      created_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def filters do
    [
      status: %{
        module: Teen.Filters.StatusSelect
      },
      payment_method: %{
        module: Teen.Filters.PaymentMethodSelect
      }
    ]
  end

  def item_actions(_) do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        only: [:row, :index, :show]
      },
      enable_config: %{
        module: Teen.ItemActions.EnableConfig,
        only: [:index]
      },
      disable_config: %{
        module: Teen.ItemActions.DisableConfig,
        only: [:index]
      }
    ]
  end

  def render_resource_slot(assigns, :index_header) do
    ~H"""
    <div class="bg-base-100 p-6 rounded-lg shadow-sm mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-base-content">提现配置管理</h2>
          <p class="text-base-content/70 mt-1">管理用户提现的配置规则和限制</p>
        </div>
        <div class="stats shadow">
          <div class="stat">
            <div class="stat-title">总配置数</div>
            <div class="stat-value text-primary">0</div>
          </div>
          <div class="stat">
            <div class="stat-title">启用中</div>
            <div class="stat-value text-success">0</div>
          </div>
          <div class="stat">
            <div class="stat-title">支付方式</div>
            <div class="stat-value text-info">3</div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  def render_resource_slot(assigns, :index_footer) do
    ~H"""
    <div class="bg-base-100 p-4 rounded-lg shadow-sm mt-6">
      <div class="text-sm text-base-content/70">
        <p>💡 提示：</p>
        <ul class="list-disc list-inside mt-2 space-y-1">
          <li>手续费率和税率以百分比形式输入，如5表示5%</li>
          <li>金额单位为分，1元 = 100分</li>
          <li>自动审核限额为空时，所有提现都需要人工审核</li>
          <li>流水倍数决定用户需要完成多少倍的有效投注才能提现</li>
        </ul>
      </div>
    </div>
    """
  end
end
