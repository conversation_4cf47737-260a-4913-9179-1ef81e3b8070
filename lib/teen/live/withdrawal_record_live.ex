defmodule Teen.Live.WithdrawalRecordLive do
  @moduledoc """
  提现记录管理页面

  提供提现记录的查看、审核和管理功能
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.PaymentSystem.WithdrawalRecord,
    ],
    layout: {Teen.Layouts, :admin},
    fluid?: true

  @impl Backpex.LiveResource
  def singular_name, do: "提现记录"

  @impl Backpex.LiveResource
  def plural_name, do: "提现记录"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      # 提现记录不允许手动创建
      :new -> false
      # 允许修改审核状态等
      :edit -> true
      # 提现记录不允许删除
      :delete -> false
      _ -> true
    end
    true
  end

  @impl Backpex.LiveResource
  def fields do
    %{
      order_id: %{
        module: Backpex.Fields.Text,
        label: "提现订单号",
        searchable: true,
        readonly: true,
        help_text: "系统生成的唯一提现订单号"
      },
      user: %{
        module: Backpex.Fields.BelongsTo,
        label: "用户",
        display_field: :username,
        live_resource: Teen.Live.UserLive,
        searchable: true,
        help_text: "申请提现的用户"
      },
      withdrawal_amount: %{
        module: Backpex.Fields.Number,
        label: "提现金额",
        help_text: "申请提现的金额（分）",
        render: fn assigns ->
          amount = assigns.value || 0
          assigns = assign(assigns, :yuan_amount, Decimal.div(amount, 100) |> Decimal.to_string())

          ~H"""
          <span class="font-mono text-lg font-semibold">¥{@yuan_amount}</span>
          """
        end
      },
      payment_method: %{
        module: Backpex.Fields.Select,
        label: "提现方式",
        options: [
          {"银行卡", "bank_card"},
          {"支付宝", "alipay"},
          {"UPI", "upi"}
        ],
        render: fn assigns ->
          case assigns.value do
            "bank_card" -> ~H"<span class=\"badge badge-primary\">银行卡</span>"
            "alipay" -> ~H"<span class=\"badge badge-info\">支付宝</span>"
            "upi" -> ~H"<span class=\"badge badge-secondary\">UPI</span>"
            _ -> ~H"<span class=\"badge badge-ghost\">未知</span>"
          end
        end
      },
      fee_amount: %{
        module: Backpex.Fields.Number,
        label: "手续费",
        help_text: "手续费金额（分）",
        render: fn assigns ->
          amount = assigns.value || 0
          assigns = assign(assigns, :yuan_amount, Decimal.div(amount, 100) |> Decimal.to_string())

          ~H"""
          <span class="font-mono text-orange-600">¥{@yuan_amount}</span>
          """
        end
      },
      actual_amount: %{
        module: Backpex.Fields.Number,
        label: "实际到账",
        help_text: "扣除手续费后的实际到账金额（分）",
        render: fn assigns ->
          amount = assigns.value || 0
          assigns = assign(assigns, :yuan_amount, Decimal.div(amount, 100) |> Decimal.to_string())

          ~H"""
          <span class="font-mono text-green-600 font-semibold">¥{@yuan_amount}</span>
          """
        end
      },
      audit_status: %{
        module: Backpex.Fields.Select,
        label: "审核状态",
        options: [
          {"待审核", 0},
          {"审核通过", 1},
          {"审核拒绝", 2}
        ],
        render: fn assigns ->
          case assigns.value do
            0 -> ~H"<span class=\"badge badge-warning\">待审核</span>"
            1 -> ~H"<span class=\"badge badge-success\">审核通过</span>"
            2 -> ~H"<span class=\"badge badge-error\">审核拒绝</span>"
            _ -> ~H"<span class=\"badge badge-ghost\">未知</span>"
          end
        end
      },
      progress_status: %{
        module: Backpex.Fields.Select,
        label: "处理状态",
        options: [
          {"排队中", 0},
          {"处理中", 1},
          {"支付成功", 2},
          {"支付失败", 3},
          {"人工处理", 4},
          {"已取消", 5}
        ],
        render: fn assigns ->
          case assigns.value do
            0 -> ~H"<span class=\"badge badge-neutral\">排队中</span>"
            1 -> ~H"<span class=\"badge badge-info\">处理中</span>"
            2 -> ~H"<span class=\"badge badge-success\">支付成功</span>"
            3 -> ~H"<span class=\"badge badge-error\">支付失败</span>"
            4 -> ~H"<span class=\"badge badge-warning\">人工处理</span>"
            5 -> ~H"<span class=\"badge badge-ghost\">已取消</span>"
            _ -> ~H"<span class=\"badge badge-ghost\">未知</span>"
          end
        end
      },
      required_turnover: %{
        module: Backpex.Fields.Number,
        label: "所需流水",
        help_text: "用户需要完成的流水金额（分）",
        render: fn assigns ->
          amount = assigns.value || 0
          assigns = assign(assigns, :yuan_amount, Decimal.div(amount, 100) |> Decimal.to_string())

          ~H"""
          <span class="font-mono">¥{@yuan_amount}</span>
          """
        end
      },
      completed_turnover: %{
        module: Backpex.Fields.Number,
        label: "已完成流水",
        help_text: "用户已完成的流水金额（分）",
        render: fn assigns ->
          amount = assigns.value || 0
          assigns = assign(assigns, :yuan_amount, Decimal.div(amount, 100) |> Decimal.to_string())

          ~H"""
          <span class="font-mono">¥{@yuan_amount}</span>
          """
        end
      },
      bank_info: %{
        module: Backpex.Fields.Textarea,
        label: "银行卡信息",
        help_text: "用户提供的银行卡信息",
        render: fn assigns ->
          case assigns.value do
            nil ->
              ~H"<span class=\"text-gray-500\">无</span>"

            info ->
              case Jason.decode(info) do
                {:ok, data} ->
                  assigns = assign(assigns, :bank_data, data)

                  ~H"""
                  <div class="text-sm">
                    <div><strong>银行:</strong> {Map.get(@bank_data, "bank_name", "未知")}</div>
                    <div>
                      <strong>卡号:</strong>
                      **** **** **** {String.slice(Map.get(@bank_data, "card_number", ""), -4, 4)}
                    </div>
                    <div><strong>户名:</strong> {Map.get(@bank_data, "account_name", "未知")}</div>
                  </div>
                  """

                {:error, _} ->
                  ~H"<span class=\"text-red-500\">格式错误</span>"
              end
          end
        end
      },
      feedback: %{
        module: Backpex.Fields.Textarea,
        label: "审核反馈",
        help_text: "审核人员的反馈信息"
      },
      auditor_id: %{
        module: Backpex.Fields.BelongsTo,
        label: "审核人",
        display_field: :username,
        live_resource: Teen.Live.UserLive,
        help_text: "审核此提现申请的管理员"
      },
      audit_time: %{
        module: Backpex.Fields.DateTime,
        label: "审核时间",
        help_text: "审核完成的时间"
      },
      ip_address: %{
        module: Backpex.Fields.Text,
        label: "申请IP",
        help_text: "用户申请提现时的IP地址"
      },
      created_at: %{
        module: Backpex.Fields.DateTime,
        label: "申请时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def filters do
    [
      audit_status: %{
        module: Teen.Filters.AuditStatusSelect,
        field: :audit_status
      },
      progress_status: %{
        module: Teen.Filters.ProgressStatusSelect,
        field: :progress_status
      },
      payment_method: %{
        module: Teen.Filters.PaymentMethodSelect,
        field: :payment_method
      }
    ]
  end

  @impl Backpex.LiveResource
  def item_actions(default_actions) do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row]
      },
      batch_approve: %{
        module: Teen.ItemActions.BatchApproveWithdrawals,
        only: [:index]
      },
      batch_reject: %{
        module: Teen.ItemActions.BatchRejectWithdrawals,
        only: [:index]
      },
      export_records: %{
        module: Teen.ItemActions.ExportWithdrawalRecords,
        only: [:index]
      }
    ]
  end

  def render_resource_slot(assigns, :index_header) do
    ~H"""
    <div class="bg-base-100 p-6 rounded-lg shadow-sm mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-base-content">提现记录管理</h2>
          <p class="text-base-content/70 mt-1">查看和管理所有用户提现申请</p>
        </div>
        <div class="stats shadow">
          <div class="stat">
            <div class="stat-title">总申请数</div>
            <div class="stat-value text-primary">0</div>
          </div>
          <div class="stat">
            <div class="stat-title">待审核</div>
            <div class="stat-value text-warning">0</div>
          </div>
          <div class="stat">
            <div class="stat-title">已完成</div>
            <div class="stat-value text-success">0</div>
          </div>
          <div class="stat">
            <div class="stat-title">今日提现额</div>
            <div class="stat-value text-info">¥0</div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
