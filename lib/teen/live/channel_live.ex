defmodule Teen.Live.ChannelLive do
  @moduledoc """
  登录渠道管理页面

  提供登录渠道的创建、查看、编辑和管理功能
  用于管理不同的客户端版本和渠道来源
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Cypridina.Accounts.Channel
    ],
    layout: {Teen.Layouts, :admin},
    fluid?: true

  @impl Backpex.LiveResource
  def singular_name, do: "登录渠道"

  @impl Backpex.LiveResource
  def plural_name, do: "渠道管理"

  @impl Backpex.LiveResource
  def mount(_params, _session, socket) do
    socket = assign(socket, :fluid?, true)
    {:ok, socket}
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      :new -> true
      :edit -> true
      :delete -> true
      _ -> false
    end
  end

  @impl Backpex.LiveResource
  def fields do
    %{
      channel_id: %{
        module: Backpex.Fields.Text,
        label: "渠道ID",
        searchable: true,
        help_text: "渠道数字ID，对应登录协议中的siteid，如501、503等"
      },
      channel_name: %{
        module: Backpex.Fields.Text,
        label: "渠道名称",
        searchable: true,
        help_text: "渠道显示名称，如'51TeenPatti'、'官方渠道'等"
      },
      package_name: %{
        module: Backpex.Fields.Text,
        label: "包名",
        searchable: true,
        help_text: "渠道包名，如'com.teenpatti.channel501'等"
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "描述",
        searchable: true,
        help_text: "渠道描述信息"
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"启用", 1},
          {"禁用", 0}
        ],
        help_text: "渠道启用状态"
      },
      config: %{
        module: Backpex.Fields.Textarea,
        label: "配置",
        help_text: "渠道特定配置（JSON格式），如游戏列表、服务器地址等",
        render: fn assigns ->
          config = assigns.value || %{}
          assigns = assign(assigns, :formatted_config, Jason.encode!(config, pretty: true))

          ~H"""
          <pre class="text-sm bg-gray-100 p-2 rounded overflow-x-auto"><%= @formatted_config %></pre>
          """
        end
      },
      user_count: %{
        module: Backpex.Fields.Number,
        label: "用户数量",
        readonly: true,
        help_text: "该渠道的用户数量统计"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def filters do
    [
      status: %{
        module: Teen.Filters.ChannelStatusSelect
      }
    ]
  end

  @impl Backpex.LiveResource
  def resource_actions do
    [
      enable_channel: %{
        module: Teen.ResourceActions.EnableChannel,
        label: "启用渠道",
        icon: "hero-check-circle",
        confirm_label: "确认启用",
        confirm_text: "确定要启用选中的渠道吗？",
        fields: []
      },
      disable_channel: %{
        module: Teen.ResourceActions.DisableChannel,
        label: "禁用渠道",
        icon: "hero-x-circle",
        confirm_label: "确认禁用",
        confirm_text: "确定要禁用选中的渠道吗？",
        fields: []
      }
    ]
  end

  def item_actions(_) do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        label: "查看"
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        label: "编辑"
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        label: "删除",
        confirm_label: "确认删除",
        confirm_text: "确定要删除这个渠道吗？此操作不可撤销。"
      }
    ]
  end

  def render_resource_slot(assigns, :index_header) do
    ~H"""
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-lg font-medium text-gray-900">登录渠道管理</h2>
          <p class="mt-1 text-sm text-gray-500">
            管理不同的客户端登录渠道，包括渠道ID、包名、配置等信息
          </p>
        </div>
        <div class="flex space-x-3">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {length(@items)} 个渠道
          </span>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            {Enum.count(@items, &(&1.status == 1))} 个启用
          </span>
        </div>
      </div>
    </div>
    """
  end

  def render_resource_slot(assigns, :index_footer) do
    ~H"""
    <div class="bg-gray-50 px-6 py-3 text-sm text-gray-500">
      <p>💡 提示：渠道ID对应登录协议中的siteid参数，用于区分不同的客户端版本。可以通过筛选器快速查找特定状态的渠道。</p>
    </div>
    """
  end

  def render_resource_slot(assigns, :form_footer) do
    ~H"""
    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
            <path
              fill-rule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-blue-800">配置说明</h3>
          <div class="mt-2 text-sm text-blue-700">
            <ul class="list-disc pl-5 space-y-1">
              <li>渠道ID必须唯一，对应客户端登录时的siteid参数</li>
              <li>包名用于区分不同的应用版本，可以为空</li>
              <li>配置字段支持JSON格式，可以存储渠道特定的设置</li>
              <li>禁用渠道后，该渠道的用户将无法正常登录</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
