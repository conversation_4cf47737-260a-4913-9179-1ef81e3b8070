defmodule Teen.PaymentSystem.AuditWithdrawalReactor do
  @moduledoc """
  处理提现审核的Reactor

  支持两种审核操作：
  1. 审核通过：保持积分在待审核账户，准备进入支付流程
  2. 审核拒绝：将积分从待审核账户退回用户账户

  补偿机制：
  - 审核拒绝时如果退款失败，记录异常并通知管理员
  - 确保审核状态和积分流转的一致性
  """
  use Ash.Reactor

  require Logger

  # 输入参数
  input :withdrawal_id
  input :auditor_id
  input :action  # :approve 或 :reject
  input :feedback  # 拒绝时的原因说明，默认为 nil

  # 步骤1: 验证提现记录和审核权限
  step :validate_audit_request do
    argument :withdrawal_id, input(:withdrawal_id)
    argument :auditor_id, input(:auditor_id)
    argument :action, input(:action)

    run fn %{withdrawal_id: withdrawal_id, auditor_id: auditor_id, action: action}, _context ->
      Logger.info("💸 [AUDIT_WITHDRAWAL] 验证审核请求 - ID: #{withdrawal_id}, 操作: #{action}")

      with {:ok, withdrawal} <- Teen.PaymentSystem.WithdrawalRecord.read(withdrawal_id),
           :ok <- validate_withdrawal_status(withdrawal),
           {:ok, auditor} <- validate_auditor_permission(auditor_id),
           :ok <- validate_action(action) do

        Logger.info("💸 [AUDIT_WITHDRAWAL] 审核验证通过")
        {:ok, %{withdrawal: withdrawal, auditor: auditor}}
      else
        {:error, reason} ->
          Logger.error("💸 [AUDIT_WITHDRAWAL] 审核验证失败: #{inspect(reason)}")
          {:error, reason}
      end
    end
  end

  # 步骤2: 根据审核决定分流处理
  step :route_audit_action do
    argument :action, input(:action)
    argument :validation_result, result(:validate_audit_request)

    run fn %{action: action, validation_result: validation_result}, _context ->
      Logger.info("💸 [AUDIT_WITHDRAWAL] 路由审核操作: #{action}")

      case action do
        :approve ->
          {:ok, %{route: :approve, data: validation_result}}

        :reject ->
          {:ok, %{route: :reject, data: validation_result}}

        _ ->
          {:error, "无效的审核操作: #{action}"}
      end
    end
  end

  # 步骤3A: 处理审核通过
  step :handle_approval do
    argument :route_result, result(:route_audit_action)
    argument :auditor_id, input(:auditor_id)

    run fn %{route_result: %{route: route, data: %{withdrawal: withdrawal}}, auditor_id: auditor_id}, _context ->
      if route == :approve do
        Logger.info("💸 [AUDIT_WITHDRAWAL] 处理审核通过 - 订单: #{withdrawal.order_id}")

        case Teen.PaymentSystem.WithdrawalRecord.approve_withdrawal(withdrawal, auditor_id: auditor_id) do
          {:ok, updated_withdrawal} ->
            Logger.info("💸 [AUDIT_WITHDRAWAL] 审核通过状态更新成功")
            {:ok, %{withdrawal: updated_withdrawal, action: :approved}}

          {:error, reason} ->
            Logger.error("💸 [AUDIT_WITHDRAWAL] 审核通过状态更新失败: #{inspect(reason)}")
            {:error, reason}
        end
      else
        {:ok, :skip}
      end
    end
  end

  # 步骤3B: 处理审核拒绝
  step :handle_rejection do
    argument :route_result, result(:route_audit_action)
    argument :auditor_id, input(:auditor_id)
    argument :feedback, input(:feedback)

    run fn args, _context ->
      if args.route_result.route == :reject do
        withdrawal = args.route_result.data.withdrawal
        Logger.info("💸 [AUDIT_WITHDRAWAL] 处理审核拒绝 - 订单: #{withdrawal.order_id}")

        # 首先更新状态
        case Teen.PaymentSystem.WithdrawalRecord.reject_withdrawal(
          withdrawal,
          auditor_id: args.auditor_id,
          feedback: args.feedback || "管理员拒绝"
        ) do
          {:ok, updated_withdrawal} ->
            Logger.info("💸 [AUDIT_WITHDRAWAL] 拒绝状态更新成功，准备退款")
            {:ok, %{withdrawal: updated_withdrawal, action: :rejected, need_refund: true}}

          {:error, reason} ->
            Logger.error("💸 [AUDIT_WITHDRAWAL] 拒绝状态更新失败: #{inspect(reason)}")
            {:error, reason}
        end
      else
        {:ok, :skip}
      end
    end
  end

  # 步骤4: 处理拒绝后的积分退回
  step :refund_points_on_rejection do
    argument :rejection_result, result(:handle_rejection)


    run fn %{rejection_result: result}, _context ->
      case result do
        %{need_refund: true, withdrawal: withdrawal} ->
          Logger.info("💸 [AUDIT_WITHDRAWAL] 执行积分退回 - 金额: #{withdrawal.withdrawal_amount}")

          pending_identifier = Cypridina.Ledger.AccountIdentifier.system(:withdrawal_pending, :XAA)
          user_identifier = Cypridina.Ledger.AccountIdentifier.user(withdrawal.user_id, :XAA)
          amount = Decimal.to_integer(withdrawal.withdrawal_amount)

          case Cypridina.Ledger.transfer(
            pending_identifier,
            user_identifier,
            amount,
            transaction_type: :refund,
            description: "提现审核拒绝退款: #{withdrawal.order_id}",
            metadata: %{
              withdrawal_id: withdrawal.id,
              order_id: withdrawal.order_id,
              reason: "audit_rejected",
              feedback: withdrawal.feedback
            }
          ) do
            {:ok, transfer} ->
              Logger.info("💸 [AUDIT_WITHDRAWAL] 积分退回成功，交易ID: #{transfer.id}")
              {:ok, %{withdrawal: withdrawal, refund_transfer: transfer}}

            {:error, reason} ->
              Logger.error("💸 [AUDIT_WITHDRAWAL] 积分退回失败: #{inspect(reason)}")
              {:error, "积分退回失败: #{inspect(reason)}"}
          end

        _ ->
          {:ok, :skip}
      end
    end
  end

  # 步骤5: 发布审核结果事件
  step :publish_audit_event do
    argument :approval_result, result(:handle_approval)
    argument :refund_result, result(:refund_points_on_rejection)

    run fn args, _context ->
      result = args.approval_result || args.refund_result

      case result do
        %{withdrawal: withdrawal, action: action} ->
          Logger.info("💸 [AUDIT_WITHDRAWAL] 发布审核事件: #{action}")

          event_data = %{
            event: :"withdrawal_#{action}",
            withdrawal_id: withdrawal.id,
            order_id: withdrawal.order_id,
            user_id: withdrawal.user_id,
            amount: withdrawal.withdrawal_amount,
            auditor_id: withdrawal.auditor_id,
            audit_time: withdrawal.audit_time,
            action: action
          }

          # 如果是拒绝，添加反馈信息
          event_data = if action == :rejected do
            Map.put(event_data, :feedback, withdrawal.feedback)
          else
            event_data
          end

          Phoenix.PubSub.broadcast(
            Cypridina.PubSub,
            "withdrawal:#{withdrawal.user_id}",
            event_data
          )

          # 同时发布给管理员频道
          Phoenix.PubSub.broadcast(
            Cypridina.PubSub,
            "withdrawal:admin",
            Map.put(event_data, :channel, :admin)
          )

          {:ok, withdrawal}

        _ ->
          {:ok, :no_event}
      end
    end
  end

  # 步骤6: 触发后续流程（仅限审核通过）
  step :trigger_payment_process do
    argument :approval_result, result(:handle_approval)

    run fn %{approval_result: result}, _context ->
      case result do
        %{withdrawal: withdrawal, action: :approved} ->
          Logger.info("💸 [AUDIT_WITHDRAWAL] 准备触发支付流程")

          # 可以选择立即触发支付流程或通过异步任务处理
          # 这里我们返回成功，让调用方决定是否立即执行支付
          {:ok, %{
            withdrawal: withdrawal,
            ready_for_payment: true,
            message: "提现已审核通过，可以进行支付处理"
          }}

        _ ->
          {:ok, :skip}
      end
    end
  end

  # 步骤4B: 处理退款失败的情况
  step :handle_refund_failure do
    argument :refund_result, result(:refund_points_on_rejection)

    run fn %{refund_result: result}, _context ->
      case result do
        {:error, reason} ->
          # 从rejection_result中获取withdrawal信息
          Logger.error("💸 [AUDIT_WITHDRAWAL] 退款失败，发送告警")

          # 发送告警通知
          Phoenix.PubSub.broadcast(
            Cypridina.PubSub,
            "system:alerts",
            %{
              alert: :withdrawal_refund_failed,
              message: "提现拒绝退款失败: #{reason}",
              severity: :high,
              timestamp: DateTime.utc_now()
            }
          )

          {:error, reason}

        _ ->
          {:ok, :skip}
      end
    end
  end

  # 私有辅助函数
  defp validate_withdrawal_status(withdrawal) do
    cond do
      withdrawal.audit_status != 0 ->
        {:error, "提现记录已被审核，当前状态: #{audit_status_text(withdrawal.audit_status)}"}

      withdrawal.result_status != 0 ->
        {:error, "提现已完成处理，不能再审核"}

      true ->
        :ok
    end
  end

  defp validate_auditor_permission(auditor_id) do
    # 验证审核员权限
    case Cypridina.Accounts.User.read(auditor_id) do
      {:ok, user} ->
        if user.role in ["admin", "finance", "auditor"] do
          {:ok, user}
        else
          {:error, "用户没有审核权限"}
        end

      {:error, _} ->
        {:error, "审核员不存在"}
    end
  end

  defp validate_action(action) when action in [:approve, :reject], do: :ok
  defp validate_action(action), do: {:error, "无效的审核操作: #{action}"}

  defp audit_status_text(0), do: "待审核"
  defp audit_status_text(1), do: "已通过"
  defp audit_status_text(2), do: "已拒绝"
  defp audit_status_text(_), do: "未知"
end
