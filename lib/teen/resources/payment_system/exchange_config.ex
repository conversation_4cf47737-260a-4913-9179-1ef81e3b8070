defmodule Teen.PaymentSystem.ExchangeConfig do
  @moduledoc """
  兑换配置资源

  管理兑换相关的配置，包括兑换比例、手续费、限额等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.PaymentSystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :config_name, :exchange_rate, :min_amount, :max_amount, :status]
  end

  postgres do
    table "exchange_configs"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_configs
    define :get_by_name
    define :enable
    define :disable
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [
        :config_name,
        :exchange_rate,
        :min_amount,
        :max_amount,
        :daily_limit,
        :fee_rate,
        :tax_rate,
        :status,
        :exchange_type,
        :vip_level_required,
        :recharge_requirement,
        :description
      ]
    end

    read :list_active_configs do
      filter expr(status == 1)
    end

    read :get_by_name do
      argument :config_name, :string, allow_nil?: false
      filter expr(config_name == ^arg(:config_name))
    end

    update :enable do
      change set_attribute(:status, 1)
    end

    update :disable do
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :config_name, :string do
      allow_nil? false
      public? true
      description "配置名称"
      constraints max_length: 100
    end

    attribute :exchange_rate, :decimal do
      allow_nil? false
      public? true
      description "兑换比例（金币:现金）"
      constraints min: Decimal.new("0")
    end

    attribute :min_amount, :decimal do
      allow_nil? false
      public? true
      description "最小兑换金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :max_amount, :decimal do
      allow_nil? false
      public? true
      description "最大兑换金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :daily_limit, :decimal do
      allow_nil? true
      public? true
      description "每日兑换限额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :fee_rate, :decimal do
      allow_nil? false
      public? true
      description "手续费率（%）"
      default Decimal.new("0")
      constraints min: Decimal.new("0"), max: Decimal.new("100")
    end

    attribute :tax_rate, :decimal do
      allow_nil? false
      public? true
      description "税率（%）"
      default Decimal.new("0")
      constraints min: Decimal.new("0"), max: Decimal.new("100")
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :exchange_type, :integer do
      allow_nil? false
      public? true
      description "兑换类型：1-游戏兑换，2-推广兑换"
      constraints min: 1, max: 2
    end

    attribute :vip_level_required, :integer do
      allow_nil? true
      public? true
      description "所需VIP等级"
      constraints min: 0
    end

    attribute :recharge_requirement, :decimal do
      allow_nil? true
      public? true
      description "充值要求（分）"
      constraints min: Decimal.new("0")
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "配置描述"
      constraints max_length: 500
    end

    timestamps()
  end

  identities do
    identity :unique_config_name, [:config_name]
  end
end
