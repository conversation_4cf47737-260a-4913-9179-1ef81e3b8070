defmodule Teen.Services.WithdrawalService do
  @moduledoc """
  提现服务层

  提供提现相关的高级业务接口：
  - 创建提现申请（含积分扣除）
  - 管理员审核操作
  - 执行支付处理
  - 处理支付回调
  - 查询和统计功能

  所有复杂操作都通过Reactor实现，确保数据一致性
  """

  require Logger
  alias Teen.PaymentSystem

  # ========== 创建提现 ==========

  @doc """
  创建提现申请

  自动执行：
  1. 验证用户资格
  2. 创建提现记录
  3. 扣除积分到待审核账户
  """
  def create_withdrawal(user_id, params) do
    Logger.info("💰 创建提现申请 - 用户: #{user_id}")
    Logger.info("💰 参数检查 - payment_method: #{inspect(params[:payment_method])}, bank_info: #{inspect(params[:bank_info])}")

    reactor_params = Map.merge(params, %{
      user_id: user_id,
      ip_address: Map.get(params, :ip_address, "127.0.0.1")
    })

    Logger.info("💰 Reactor参数 - payment_method: #{inspect(reactor_params[:payment_method])}")

    case Reactor.run(PaymentSystem.CreateWithdrawalReactor, reactor_params) do
      {:ok, withdrawal} ->
        Logger.info("💰 提现申请创建成功: #{withdrawal.order_id}")
        {:ok, withdrawal}

      {:error, reason} ->
        Logger.error("💰 提现申请创建失败: #{inspect(reason)}")
        {:error, format_error(reason)}

      # 处理意外的返回值
      other ->
        Logger.error("💰 提现申请返回意外结果: #{inspect(other)}")
        {:error, "提现申请处理异常"}
    end
  end

  # ========== 审核操作 ==========

  @doc """
  审核通过提现申请

  保持积分在待审核账户，准备进入支付流程
  """
  def approve_withdrawal(withdrawal_id, auditor_id) do
    Logger.info("💰 审核通过提现: #{withdrawal_id}")

    case Reactor.run(PaymentSystem.AuditWithdrawalReactor, %{
      withdrawal_id: withdrawal_id,
      auditor_id: auditor_id,
      action: :approve,
      feedback: "审核通过"
    }) do
      {:ok, result} ->
        Logger.info("💰 提现审核通过: #{result.withdrawal.order_id}")
        {:ok, result.withdrawal}

      {:error, reason} ->
        Logger.error("💰 提现审核失败: #{inspect(reason)}")
        {:error, format_error(reason)}
    end
  end

  @doc """
  审核拒绝提现申请

  自动将积分从待审核账户退回用户账户
  """
  def reject_withdrawal(withdrawal_id, auditor_id, feedback) do
    Logger.info("💰 审核拒绝提现: #{withdrawal_id}")

    case Reactor.run(PaymentSystem.AuditWithdrawalReactor, %{
      withdrawal_id: withdrawal_id,
      auditor_id: auditor_id,
      action: :reject,
      feedback: feedback
    }) do
      {:ok, result} ->
        Logger.info("💰 提现审核拒绝: #{result.withdrawal.order_id}")
        {:ok, result.withdrawal}

      {:error, reason} ->
        Logger.error("💰 提现拒绝失败: #{inspect(reason)}")
        {:error, format_error(reason)}
    end
  end

  # ========== 支付处理 ==========

  @doc """
  执行提现支付

  仅处理已审核通过的提现申请
  如果支付失败，自动退回积分
  """
  def process_withdrawal_payment(withdrawal_id, gateway_config \\ nil) do
    Logger.info("💰 执行提现支付: #{withdrawal_id}")

    params = %{withdrawal_id: withdrawal_id}
    params = if gateway_config, do: Map.put(params, :gateway_config, gateway_config), else: params

    case Reactor.run(PaymentSystem.CompleteWithdrawalReactor, params) do
      {:ok, _result} ->
        Logger.info("💰 提现支付提交成功")
        {:ok, :submitted}

      {:error, reason} ->
        Logger.error("💰 提现支付失败: #{inspect(reason)}")
        {:error, format_error(reason)}
    end
  end

  @doc """
  批量处理已审核的提现

  适用于定时任务或批量操作
  """
  def process_approved_withdrawals(limit \\ 10) do
    Logger.info("💰 批量处理已审核提现，限制: #{limit}")

    # 查询已审核待处理的提现
    case PaymentSystem.WithdrawalRecord.list_by_audit_status(1) do
      {:ok, withdrawals} ->
        withdrawals
        |> Enum.filter(&(&1.progress_status == 0))
        |> Enum.take(limit)
        |> Enum.map(fn withdrawal ->
          case process_withdrawal_payment(withdrawal.id) do
            {:ok, _} -> {:ok, withdrawal.id}
            {:error, reason} -> {:error, {withdrawal.id, reason}}
          end
        end)

      {:error, reason} ->
        Logger.error("💰 查询待处理提现失败: #{inspect(reason)}")
        []
    end
  end

  # ========== 回调处理 ==========

  @doc """
  处理支付网关回调

  根据支付结果自动处理积分流转：
  - 成功：积分转入已支付账户
  - 失败：积分退回用户账户
  """
  def handle_withdrawal_callback(order_id, callback_data, signature \\ nil) do
    Logger.info("💰 处理提现回调: #{order_id}")

    case Reactor.run(PaymentSystem.WithdrawalCallbackReactor, %{
      order_id: order_id,
      callback_data: callback_data,
      gateway_signature: signature
    }) do
      {:ok, _result} ->
        Logger.info("💰 提现回调处理成功")
        {:ok, :processed}

      {:error, reason} ->
        Logger.error("💰 提现回调处理失败: #{inspect(reason)}")
        {:error, format_error(reason)}
    end
  end

  # ========== 查询功能 ==========

  @doc """
  获取用户提现统计
  """
  def get_user_withdrawal_stats(user_id) do
    case PaymentSystem.WithdrawalRecord.get_user_withdrawal_stats(user_id) do
      {:ok, stats} -> {:ok, format_stats(stats)}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  获取用户最近的提现记录
  """
  def get_user_recent_withdrawals(user_id, days \\ 30) do
    PaymentSystem.WithdrawalRecord.list_user_recent_withdrawals(user_id, days)
  end

  @doc """
  检查用户是否可以提现
  """
  def check_withdrawal_eligibility(user_id, amount) do
    with {:ok, balance} <- get_user_balance(user_id),
         :ok <- check_balance(balance, amount),
         {:ok, turnover_info} <- Teen.Services.TurnoverService.get_user_turnover_info(user_id),
         :ok <- check_turnover(turnover_info),
         :ok <- check_daily_limit(user_id, amount),
         :ok <- check_user_status(user_id) do
      {:ok, %{
        eligible: true,
        balance: balance,
        turnover_info: turnover_info
      }}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  # ========== 管理功能 ==========

  @doc """
  获取待审核的提现列表
  """
  def get_pending_audit_withdrawals(opts \\ []) do
    limit = Keyword.get(opts, :limit, 50)
    offset = Keyword.get(opts, :offset, 0)

    PaymentSystem.WithdrawalRecord.list_by_audit_status(0)
    |> apply_pagination(limit, offset)
  end

  @doc """
  获取异常提现列表（需要人工介入）
  """
  def get_exception_withdrawals do
    # 查询所有状态异常的提现
    # 例如：长时间处理中、积分扣除但未审核等
    {:ok, []}  # TODO: 实现异常检测逻辑
  end

  @doc """
  重试失败的提现
  """
  def retry_failed_withdrawal(withdrawal_id) do
    with {:ok, withdrawal} <- PaymentSystem.WithdrawalRecord.read(withdrawal_id),
         :ok <- validate_can_retry(withdrawal),
         {:ok, _} <- reset_withdrawal_status(withdrawal),
         {:ok, _} <- process_withdrawal_payment(withdrawal_id) do
      {:ok, :retrying}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  # ========== 兼容旧接口 ==========

  @doc """
  处理提现申请（兼容旧接口）
  """
  def process_withdrawal(withdrawal_id) do
    Logger.warning("💰 使用旧接口 process_withdrawal，请更新为 process_withdrawal_payment")
    process_withdrawal_payment(withdrawal_id)
  end

  @doc """
  处理提现回调完成（兼容旧接口）
  """
  def complete_withdrawal(order_id, callback_result) do
    Logger.warning("💰 使用旧接口 complete_withdrawal，请更新为 handle_withdrawal_callback")

    callback_data = %{
      "order_id" => order_id,
      "status" => to_string(callback_result.status),
      "gateway_order_id" => callback_result[:gateway_order_id],
      "message" => callback_result[:message]
    }

    handle_withdrawal_callback(order_id, callback_data)
  end

  @doc """
  处理提现失败（兼容旧接口）
  """
  def fail_withdrawal(order_id, error_data) do
    Logger.warning("💰 使用旧接口 fail_withdrawal，请更新为 handle_withdrawal_callback")

    callback_data = %{
      "order_id" => order_id,
      "status" => "failed",
      "message" => error_data[:error_message] || "Withdrawal failed"
    }

    handle_withdrawal_callback(order_id, callback_data)
  end

  # ========== 私有辅助函数 ==========

  defp get_user_balance(user_id) do
    user_identifier = Cypridina.Ledger.AccountIdentifier.user(user_id, :XAA)

    case Cypridina.Ledger.BalanceCache.get_balance(user_identifier) do
      {:ok, balance} -> {:ok, balance}
      {:error, _} -> {:error, "获取余额失败"}
    end
  end

  defp check_balance(balance, amount) do
    amount_int = if is_integer(amount), do: amount, else: Decimal.to_integer(amount)

    if balance >= amount_int do
      :ok
    else
      {:error, "余额不足"}
    end
  end

  defp check_turnover(turnover_info) do
    if turnover_info.is_eligible do
      :ok
    else
      {:error, "流水未达标"}
    end
  end

  defp check_daily_limit(_user_id, _amount) do
    # TODO: 实现每日提现限额检查
    :ok
  end

  defp check_user_status(user_id) do
    case Cypridina.Accounts.User.read(user_id) do
      {:ok, user} ->
        cond do
          user.status != "active" -> {:error, "用户状态异常"}
          user.payment_banned -> {:error, "用户支付功能被限制"}
          true -> :ok
        end

      {:error, _} -> {:error, "用户不存在"}
    end
  end

  defp validate_can_retry(withdrawal) do
    cond do
      withdrawal.result_status != 2 ->
        {:error, "只能重试失败的提现"}

      withdrawal.retry_count >= 3 ->
        {:error, "重试次数超限"}

      true ->
        :ok
    end
  end

  defp reset_withdrawal_status(withdrawal) do
    PaymentSystem.WithdrawalRecord.update(withdrawal, %{
      progress_status: 0,
      result_status: 0,
      retry_count: (withdrawal.retry_count || 0) + 1,
      last_retry_time: DateTime.utc_now()
    })
  end

  defp format_error(error) when is_binary(error), do: error
  defp format_error(error), do: inspect(error)

  defp format_stats(stats) do
    %{
      total_count: Map.get(stats, :total_count, 0),
      total_amount: Map.get(stats, :total_amount, Decimal.new(0)),
      success_count: Map.get(stats, :success_count, 0),
      success_amount: Map.get(stats, :success_amount, Decimal.new(0)),
      pending_count: Map.get(stats, :pending_count, 0),
      pending_amount: Map.get(stats, :pending_amount, Decimal.new(0))
    }
  end

  defp apply_pagination({:ok, records}, limit, offset) do
    paginated = records
    |> Enum.drop(offset)
    |> Enum.take(limit)

    {:ok, paginated}
  end

  defp apply_pagination(error, _, _), do: error
end
