defmodule CypridinaWeb.ThemeLive do
  @moduledoc """
  主题管理和预览页面
  """

  use CypridinaWeb, :live_view
  alias CypridinaWeb.Components.ThemeSwitcher
  alias CypridinaWeb.ThemeConfig

  @impl true
  def mount(_params, session, socket) do
    current_theme = get_current_theme(session)

    socket =
      socket
      |> assign(:current_theme, current_theme)
      |> assign(:themes, ThemeConfig.get_all_themes())
      |> assign(:theme_stats, ThemeConfig.get_theme_stats())
      |> assign(:recommended_themes, ThemeConfig.get_recommended_themes())
      |> assign(:preview_theme, current_theme)
      |> assign(:show_preview, false)
      |> assign(:page_title, "主题管理")

    {:ok, socket}
  end

  @impl true
  def handle_event("change_theme", %{"theme" => theme}, socket) do
    if ThemeConfig.theme_exists?(theme) do
      # 更新当前主题
      socket =
        socket
        |> assign(:current_theme, theme)
        |> put_flash(:info, "主题已切换到 #{get_theme_label(theme)}")

      # 通过 JavaScript 更新页面主题
      {:noreply, push_event(socket, "theme-changed", %{theme: theme})}
    else
      {:noreply, put_flash(socket, :error, "无效的主题")}
    end
  end

  @impl true
  def handle_event("toggle_theme", _params, socket) do
    new_theme = if socket.assigns.current_theme == "light", do: "dark", else: "light"

    socket =
      socket
      |> assign(:current_theme, new_theme)
      |> put_flash(:info, "已切换到 #{get_theme_label(new_theme)} 主题")

    {:noreply, push_event(socket, "theme-changed", %{theme: new_theme})}
  end

  @impl true
  def handle_event("preview_theme", %{"theme" => theme}, socket) do
    socket =
      socket
      |> assign(:preview_theme, theme)
      |> assign(:show_preview, true)

    {:noreply, push_event(socket, "preview-theme", %{theme: theme})}
  end

  @impl true
  def handle_event("apply_preview", _params, socket) do
    preview_theme = socket.assigns.preview_theme

    socket =
      socket
      |> assign(:current_theme, preview_theme)
      |> assign(:show_preview, false)
      |> put_flash(:info, "已应用 #{get_theme_label(preview_theme)} 主题")

    {:noreply, push_event(socket, "theme-changed", %{theme: preview_theme})}
  end

  @impl true
  def handle_event("cancel_preview", _params, socket) do
    current_theme = socket.assigns.current_theme

    socket =
      socket
      |> assign(:show_preview, false)
      |> assign(:preview_theme, current_theme)

    {:noreply, push_event(socket, "theme-changed", %{theme: current_theme})}
  end

  @impl true
  def handle_event("reset_theme", _params, socket) do
    default_theme = "light"

    socket =
      socket
      |> assign(:current_theme, default_theme)
      |> assign(:preview_theme, default_theme)
      |> assign(:show_preview, false)
      |> put_flash(:info, "已重置为默认主题")

    {:noreply, push_event(socket, "theme-changed", %{theme: default_theme})}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-base-100 transition-all duration-300">
      <!-- 页面头部 -->
      <div class="bg-base-200 border-b border-base-300">
        <div class="container mx-auto px-4 py-6">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-3xl font-bold text-base-content">主题管理</h1>
              <p class="text-base-content/70 mt-1">个性化您的界面体验</p>
            </div>

            <div class="flex items-center gap-4">
              <!-- 当前主题显示 -->
              <div class="bg-base-100 rounded-lg px-4 py-2 border border-base-300">
                <div class="flex items-center gap-2">
                  <span class="text-lg"><%= get_theme_icon(@current_theme) %></span>
                  <div>
                    <div class="text-sm font-medium text-base-content">当前主题</div>
                    <div class="text-xs text-base-content/70"><%= get_theme_label(@current_theme) %></div>
                  </div>
                </div>
              </div>

              <!-- 主题切换器 -->
              <ThemeSwitcher.theme_switcher current_theme={@current_theme} show_label={true} />
            </div>
          </div>
        </div>
      </div>

      <!-- 预览模式提示 -->
      <%= if @show_preview do %>
        <div class="bg-warning/10 border-b border-warning/20">
          <div class="container mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <.icon name="hero-eye" class="w-5 h-5 text-warning" />
                <span class="text-warning font-medium">
                  正在预览 <%= get_theme_label(@preview_theme) %> 主题
                </span>
              </div>
              <div class="flex items-center gap-2">
                <button
                  class="btn btn-sm btn-success"
                  phx-click="apply_preview"
                >
                  应用主题
                </button>
                <button
                  class="btn btn-sm btn-ghost"
                  phx-click="cancel_preview"
                >
                  取消预览
                </button>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <!-- 主要内容 -->
      <div class="container mx-auto px-4 py-8">
        <!-- 快速操作 -->
        <div class="mb-8">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-base-content">快速操作</h2>
            <button
              class="btn btn-outline btn-sm"
              phx-click="reset_theme"
            >
              <.icon name="hero-arrow-path" class="w-4 h-4" />
              重置为默认
            </button>
          </div>

          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button
              class="btn btn-outline"
              phx-click="toggle_theme"
            >
              <.icon name="hero-sun" class="w-5 h-5" />
              切换明暗
            </button>
            <button
              class="btn btn-outline"
              phx-click="change_theme"
              phx-value-theme="modern-blue"
            >
              💙 现代蓝
            </button>
            <button
              class="btn btn-outline"
              phx-click="change_theme"
              phx-value-theme="nature-green"
            >
              🌿 自然绿
            </button>
            <button
              class="btn btn-outline"
              phx-click="change_theme"
              phx-value-theme="warm-orange"
            >
              🧡 温暖橙
            </button>
          </div>
        </div>

        <!-- 主题网格 -->
        <div class="mb-8">
          <h2 class="text-xl font-semibold text-base-content mb-4">所有主题</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <%= for theme <- @themes do %>
              <div class={[
                "theme-preview-card p-4 rounded-lg border-2 cursor-pointer transition-all hover:scale-105 animate-fade-in-up",
                if(theme.name == @current_theme, do: "border-primary bg-primary/5", else: "border-base-300 hover:border-primary/50")
              ]}
              phx-click="change_theme"
              phx-value-theme={theme.name}
              data-theme={theme.name}>
                <div class="flex items-center gap-3 mb-3">
                  <span class="text-2xl"><%= theme.icon %></span>
                  <div>
                    <h4 class="font-semibold text-base-content"><%= theme.label %></h4>
                    <p class="text-sm text-base-content/70 capitalize"><%= theme.category %></p>
                  </div>
                  <%= if theme.is_dark do %>
                    <div class="badge badge-neutral badge-sm">深色</div>
                  <% else %>
                    <div class="badge badge-ghost badge-sm">浅色</div>
                  <% end %>
                </div>

                <div class="space-y-2">
                  <div class="flex gap-2">
                    <div class="flex-1 h-2 bg-primary rounded"></div>
                    <div class="flex-1 h-2 bg-secondary rounded"></div>
                    <div class="flex-1 h-2 bg-accent rounded"></div>
                  </div>
                  <div class="flex gap-2">
                    <div class="flex-1 h-2 bg-base-200 rounded"></div>
                    <div class="flex-1 h-2 bg-base-300 rounded"></div>
                  </div>
                </div>

                <p class="text-xs text-base-content/60 mt-3 line-clamp-2">
                  <%= theme.description %>
                </p>

                <%= if theme.name == @current_theme do %>
                  <div class="mt-3 flex items-center gap-2 text-primary">
                    <.icon name="hero-check" class="w-4 h-4" />
                    <span class="text-sm font-medium">当前主题</span>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>

        <!-- 推荐主题 -->
        <div class="mb-8">
          <h2 class="text-xl font-semibold text-base-content mb-4">推荐主题</h2>
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <%= for theme <- @recommended_themes do %>
              <div class="theme-quick-card p-4 bg-base-100 rounded-lg border border-base-300 hover:border-primary/50 hover:bg-primary/5 transition-all cursor-pointer"
                   phx-click="change_theme" phx-value-theme={theme.name}>
                <div class="text-center">
                  <div class="text-2xl mb-2"><%= theme.icon %></div>
                  <div class="text-sm font-medium text-base-content"><%= theme.label %></div>
                  <div class="text-xs text-base-content/60 mt-1"><%= theme.category %></div>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- 主题统计 -->
        <div class="bg-base-200 rounded-lg p-6">
          <h3 class="text-lg font-semibold text-base-content mb-4">主题统计</h3>
          <div class="grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="stat">
              <div class="stat-title">总主题数</div>
              <div class="stat-value text-primary"><%= @theme_stats.total %></div>
            </div>
            <div class="stat">
              <div class="stat-title">基础主题</div>
              <div class="stat-value text-secondary"><%= @theme_stats.basic %></div>
            </div>
            <div class="stat">
              <div class="stat-title">自定义主题</div>
              <div class="stat-value text-accent"><%= @theme_stats.custom %></div>
            </div>
            <div class="stat">
              <div class="stat-title">DaisyUI主题</div>
              <div class="stat-value text-info"><%= @theme_stats.daisyui %></div>
            </div>
            <div class="stat">
              <div class="stat-title">浅色主题</div>
              <div class="stat-value text-success"><%= @theme_stats.light %></div>
            </div>
            <div class="stat">
              <div class="stat-title">深色主题</div>
              <div class="stat-value text-warning"><%= @theme_stats.dark %></div>
            </div>
          </div>

          <div class="mt-4 p-4 bg-base-100 rounded-lg">
            <div class="flex items-center justify-between">
              <div>
                <span class="text-sm font-medium text-base-content">当前主题:</span>
                <span class="ml-2 text-lg"><%= get_theme_icon(@current_theme) %></span>
                <span class="ml-1 font-semibold text-primary"><%= get_theme_label(@current_theme) %></span>
              </div>
              <div class="text-xs text-base-content/60">
                <%= case ThemeConfig.get_theme(@current_theme) do %>
                  <% %{category: category, is_dark: is_dark} -> %>
                    <%= String.upcase(to_string(category)) %> · <%= if is_dark, do: "深色", else: "浅色" %>
                  <% _ -> %>
                    未知主题
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # 私有函数

  defp get_current_theme(session) do
    session["theme"] || "light"
  end

  defp get_theme_icon(theme_name) do
    case ThemeConfig.get_theme(theme_name) do
      %{icon: icon} -> icon
      _ -> "🎨"
    end
  end

  defp get_theme_label(theme_name) do
    case ThemeConfig.get_theme(theme_name) do
      %{label: label} -> label
      _ -> theme_name
    end
  end
end
